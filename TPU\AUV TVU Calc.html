<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ocean Infinity AUV THU Calculator with IHO S-44</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; background-color: #f9fafb; }
        .calculator-container { max-width: 1100px; margin: 2rem auto; padding: 2rem; background-color: #fff; border-radius: 0.75rem; box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05); }
        .section-title { font-size: 1.25rem; font-weight: 600; color: #111827; margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 1px solid #e5e7eb; }
        .input-group { margin-bottom: 1.5rem; }
        .input-group label, .select-group label { display: block; margin-bottom: 0.25rem; color: #374151; font-weight: 500; }
        .input-description { font-size: 0.8rem; color: #6B7280; margin-bottom: 0.5rem; line-height: 1.4; }
        .input-field-container { display: flex; align-items: center; }
        .input-field, .select-field { flex-grow: 1; padding: 0.75rem; border: 1px solid #D1D5DB; border-radius: 0.5rem; transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out; }
        .input-field:focus, .select-field:focus { border-color: #3B82F6; box-shadow: 0 0 0 3px rgba(59,130,246,0.3); outline: none; }
        .input-slider { flex-grow: 1; margin-right: 0.75rem; accent-color: #3B82F6; height: 20px; }
        .slider-value-display { width: 70px; text-align: right; font-weight: 500; color: #1F2937; padding-left: 0.5rem; }
        .unit-text-input { margin-left: 0.5rem; font-size: 0.875rem; color: #4B5563; }
        
        .results-card { background-color: #F3F4F6; padding: 1.5rem; border-radius: 0.75rem; margin-top: 2rem; }
        .result-value { font-size: 1.5rem; font-weight: 700; color: #1F2937; }
        .unit-text-result { font-size: 0.875rem; color: #4B5563; }
        .info-text { font-size: 0.875rem; color: #6B7280; margin-top: 1rem; }

        .iho-compliance-section { margin-top: 2rem; padding-top: 1.5rem; border-top: 1px solid #e5e7eb;}
        .iho-status-pass { color: #16A34A; font-weight: bold; } /* green-600 */
        .iho-status-fail { color: #DC2626; font-weight: bold; } /* red-600 */
        .iho-status-note { color: #F59E0B; font-weight: bold; } /* amber-500 */


        .chart-container { margin-top: 2.5rem; padding: 1.5rem; background-color: #F9FAFB; border-radius: 0.75rem; border: 1px solid #E5E7EB; }
        .chart-title { font-size: 1.125rem; font-weight: 600; color: #374151; margin-bottom: 1rem; }
        .chart-bar-group { display: flex; align-items: center; margin-bottom: 0.75rem; font-size: 0.875rem; }
        .chart-bar-label { width: 220px; flex-shrink: 0; color: #4B5563; padding-right: 0.5rem; text-align: right; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
        .chart-bar { height: 20px; border-radius: 0.25rem; transition: width 0.3s ease-in-out; min-width: 1px; }
        .chart-bar-value { margin-left: 0.75rem; color: #1F2937; font-weight: 500; }
        .hidden { display: none; }
    </style>
</head>
<body>

    <div class="calculator-container">
        <header class="mb-8 text-center">
            <h1 class="text-3xl font-bold text-gray-800">Ocean Infinity AUV THU Calculator with IHO S-44 Compliance</h1>
            <p class="mt-2 text-md text-gray-600">Interactive tool for AUV navigation uncertainty planning, featuring specific sensor models, operational scenarios, and IHO S-44 Ed. 6 checks.</p>
        </header>

        <main>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-x-8">
                <div> <section class="mb-6">
                        <h2 class="section-title">1. INS Configuration (Honeywell HG9900)</h2>
                        <div class="input-group">
                            <label for="ins_mode">INS Operating Mode:</label>
                            <select id="ins_mode" class="select-field">
                                <option value="dvl_aided">DVL Aided (HG9900 + Bottom Lock)</option>
                                <option value="unaided">Unaided (HG9900 Midwater Drift)</option>
                            </select>
                        </div>

                        <div id="unaided_ins_controls" class="hidden">
                            <div class="input-group">
                                <label for="time_unaided_ins_minutes">Time Since Last Alignment/Fix (Unaided):</label>
                                <p class="input-description">Duration HG9900 operates without aiding. Assumes approx. 0.8-2.5 m/min (1σ radial) drift (equivalent to 50-150 m/hr).</p>
                                <div class="input-field-container">
                                    <input type="range" id="time_unaided_ins_minutes" min="0" max="120" step="1" value="30" class="input-slider">
                                    <span id="time_unaided_ins_minutes_value" class="slider-value-display">30</span>
                                    <span class="unit-text-input">[minutes]</span>
                                </div>
                            </div>
                        </div>

                        <div id="dvl_aided_ins_controls">
                            <div class="input-group">
                                <label for="time_dvl_aided_ins_minutes">Time Since Last Absolute Fix (DVL Aided):</label>
                                <p class="input-description">Duration HG9900 + DVL operate since last GPS/USBL update. Key for planning update frequency.</p>
                                <div class="input-field-container">
                                    <input type="range" id="time_dvl_aided_ins_minutes" min="0" max="120" step="1" value="30" class="input-slider">
                                    <span id="time_dvl_aided_ins_minutes_value" class="slider-value-display">30</span>
                                    <span class="unit-text-input">[minutes]</span>
                                </div>
                            </div>
                            <div class="input-group">
                                <label for="auv_speed_knots">AUV Speed:</label>
                                <p class="input-description">Average speed during DVL-aided leg. Used to calculate distance travelled for drift estimation (e.g., 0.05%-0.2% of DT for HG9900+DVL).</p>
                                <div class="input-field-container">
                                    <input type="range" id="auv_speed_knots" min="1" max="10" step="0.1" value="3" class="input-slider">
                                    <span id="auv_speed_knots_value" class="slider-value-display">3.0</span>
                                    <span class="unit-text-input">[knots]</span>
                                </div>
                            </div>
                        </div>
                    </section>

                    <section class="mb-6">
                        <h2 class="section-title">2. Acoustic Fix Configuration (Kongsberg HiPAP 501)</h2>
                        <div class="input-group">
                            <label for="hipap_slant_range_m">HiPAP Slant Range (Layback):</label>
                            <p class="input-description">Distance to transponder. HiPAP 501 accuracy often cited as ~0.2% of slant range (1σ) with good conditions/transponder.</p>
                            <div class="input-field-container">
                                <input type="range" id="hipap_slant_range_m" min="0" max="1000" step="10" value="150" class="input-slider">
                                <span id="hipap_slant_range_m_value" class="slider-value-display">150</span>
                                <span class="unit-text-input">[m]</span>
                            </div>
                             <p class="input-description mt-1">Calculated HiPAP Fix Error (σ<sub>Acoustic</sub>): <strong id="calculated_sigma_acoustic">0.00 m</strong></p>
                        </div>
                    </section>

                    <section class="mb-6">
                        <h2 class="section-title">3. Surface Fix Configuration (GNSS)</h2>
                        <div class="input-group">
                            <label for="surface_gps_quality">GNSS Fix Quality:</label>
                            <p class="input-description">Select the type of GNSS correction available for surface fixes.</p>
                            <select id="surface_gps_quality" class="select-field">
                                <option value="2.5">Uncorrected GNSS (σ ≈ 2.5m)</option>
                                <option value="0.5">Differential GNSS (DGPS/RTK Float) (σ ≈ 0.5m)</option>
                                <option value="0.05">Veripos Apex/Ultra (PPP/RTK Fixed) (σ ≈ 0.05m)</option>
                            </select>
                            <p class="input-description mt-1">Selected GNSS Fix Error (σ<sub>Surface</sub>): <strong id="selected_sigma_surface">2.50 m</strong></p>
                        </div>
                         <div class="input-group">
                            <label for="sigma_gnss_offset">GNSS Antenna Offset Error (σ<sub>Offset</sub>):</label>
                            <p class="input-description">Uncertainty in the measured 3D offset from GNSS antenna to AUV reference point (dimensional control).</p>
                            <div class="input-field-container">
                                <input type="range" id="sigma_gnss_offset" min="0" max="0.5" step="0.01" value="0.02" class="input-slider">
                                <span id="sigma_gnss_offset_value" class="slider-value-display">0.02</span>
                                <span class="unit-text-input">[m]</span>
                            </div>
                        </div>
                    </section>

                    <section class="mb-6">
                        <h2 class="section-title">4. Other Error Sources</h2>
                        <div class="input-group">
                            <label for="sigma_ss">Sound Speed Error (σ<sub>SS</sub>):</label>
                            <p class="input-description">Positional error from SVP uncertainty (e.g., using Valeport Midas SVX2 data). Depends on environment & measurement quality.</p>
                            <div class="input-field-container">
                                <input type="range" id="sigma_ss" min="0" max="5" step="0.05" value="0.5" class="input-slider">
                                <span id="sigma_ss_value" class="slider-value-display">0.50</span>
                                <span class="unit-text-input">[m]</span>
                            </div>
                        </div>
                        <div class="input-group">
                            <label for="sigma_deploy">Deployment/Calibration Error (σ<sub>Deploy</sub>):</label>
                            <p class="input-description">Initial position uncertainty from deployment or residual calibration errors (e.g., INS alignment).</p>
                            <div class="input-field-container">
                                <input type="range" id="sigma_deploy" min="0" max="5" step="0.1" value="1.0" class="input-slider">
                                <span id="sigma_deploy_value" class="slider-value-display">1.0</span>
                                <span class="unit-text-input">[m]</span>
                            </div>
                        </div>
                        <div class="input-group">
                            <label for="sigma_other">Other Unmodeled Errors (σ<sub>Other</sub>):</label>
                            <p class="input-description">Catch-all for minor error sources not explicitly listed.</p>
                            <div class="input-field-container">
                                <input type="range" id="sigma_other" min="0" max="2" step="0.05" value="0.25" class="input-slider">
                                <span id="sigma_other_value" class="slider-value-display">0.25</span>
                                <span class="unit-text-input">[m]</span>
                            </div>
                        </div>
                    </section>
                </div> <div> <div class="results-card">
                        <h2 class="text-xl font-semibold text-gray-700 mb-4">Calculated Results</h2>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm font-medium text-gray-600">Combined Horizontal Sigma (σ<sub>H_total</sub>)</p>
                                <p id="result_sigma_total" class="result-value">0.00 <span class="unit-text-result">m</span></p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-600">Total Horizontal Uncertainty (THU<sub>95%</sub>)</p>
                                <p id="result_thu_95" class="result-value">0.00 <span class="unit-text-result">m</span></p>
                            </div>
                        </div>
                         <p class="info-text">
                            THU<sub>95%</sub> = 2 * σ<sub>H_total</sub>. Assumes independent, normally distributed errors. Review sensor-specific constants in script for accuracy.
                        </p>
                    </div>

                    <div class="iho-compliance-section results-card mt-6">
                        <h2 class="text-xl font-semibold text-gray-700 mb-4">IHO S-44 Compliance (Ed. 6.0.0)</h2>
                        <div class="input-group">
                            <label for="iho_order_select">Target IHO Survey Order:</label>
                            <select id="iho_order_select" class="select-field">
                                <option value="exclusive">Exclusive Order (0.5m)</option>
                                <option value="special">Special Order (1.0m)</option>
                                <option value="1a">Order 1a (2.0m)</option>
                                <option value="1b" selected>Order 1b (5.0m)</option>
                                <option value="2a">Order 2a (10.0m)</option>
                                <option value="2b">Order 2b (20.0m)</option>
                                <option value="none">None (No Check)</option>
                            </select>
                        </div>
                        <p class="text-sm font-medium text-gray-600">IHO THU<sub>95%</sub> Limit: <strong id="iho_limit_display">5.00 m</strong></p>
                        <p class="text-sm font-medium text-gray-600 mt-2">Compliance Status: <strong id="iho_status_display" class="iho-status-pass">PASS</strong></p>
                    </div>
        
                    <div class="chart-container mt-6">
                        <h3 class="chart-title">Error Source Contribution (Variance σ<sup>2</sup>)</h3>
                        <div id="errorChart"></div>
                    </div>
                </div> </div> </main>

        <footer class="mt-8 py-6 text-center border-t border-gray-200">
            <p class="text-xs text-gray-500">
                This calculator provides estimations. Verify all assumptions and default sensor performance values.
                Consult specific equipment documentation and IHO S-44 Ed. 6.0.0 for accurate hydrographic survey analysis.
            </p>
        </footer>
    </div>

    <script>
        // --- Configuration Constants (User should verify/adjust these for their specific setup) ---
        const HG9900_UNAIDED_DRIFT_RATE_M_PER_SIGMA_HR = 75.0; // Illustrative: 1-sigma radial error growth (m/hr)
        const HG9900_DVL_AIDED_DRIFT_PERCENT_DT = 0.001;   // Illustrative: 0.1% of Distance Travelled (1-sigma)
        const HIPAP_ACCURACY_PERCENT_SLANT_RANGE = 0.002;  // Illustrative: 0.2% of Slant Range (1-sigma)
        const KNOTS_TO_MPS = 0.514444;

        const IHO_S44_LIMITS = { // THU95% limits in meters
            'exclusive': 0.5,
            'special': 1.0,
            '1a': 2.0,
            '1b': 5.0,
            '2a': 10.0,
            '2b': 20.0,
            'none': Infinity 
        };

        // --- DOM Elements ---
        const insModeSelect = document.getElementById('ins_mode');
        const unaidedInsControls = document.getElementById('unaided_ins_controls');
        const dvlAidedInsControls = document.getElementById('dvl_aided_ins_controls');

        const timeUnaidedInsMinutesSlider = document.getElementById('time_unaided_ins_minutes');
        const timeDvlAidedInsMinutesSlider = document.getElementById('time_dvl_aided_ins_minutes');
        const auvSpeedKnotsSlider = document.getElementById('auv_speed_knots');
        
        const hipapSlantRangeSlider = document.getElementById('hipap_slant_range_m');
        const surfaceGpsQualitySelect = document.getElementById('surface_gps_quality');
        const sigmaGnssOffsetSlider = document.getElementById('sigma_gnss_offset');
        
        const sigmaSsSlider = document.getElementById('sigma_ss');
        const sigmaDeploySlider = document.getElementById('sigma_deploy');
        const sigmaOtherSlider = document.getElementById('sigma_other');

        const ihoOrderSelect = document.getElementById('iho_order_select');
        const ihoLimitDisplay = document.getElementById('iho_limit_display');
        const ihoStatusDisplay = document.getElementById('iho_status_display');


        // Value displays for sliders
        const sliderValueDisplays = {
            'time_unaided_ins_minutes': document.getElementById('time_unaided_ins_minutes_value'),
            'time_dvl_aided_ins_minutes': document.getElementById('time_dvl_aided_ins_minutes_value'),
            'auv_speed_knots': document.getElementById('auv_speed_knots_value'),
            'hipap_slant_range_m': document.getElementById('hipap_slant_range_m_value'),
            'sigma_gnss_offset': document.getElementById('sigma_gnss_offset_value'),
            'sigma_ss': document.getElementById('sigma_ss_value'),
            'sigma_deploy': document.getElementById('sigma_deploy_value'),
            'sigma_other': document.getElementById('sigma_other_value'),
        };
        
        // Calculated value displays
        const calculatedSigmaAcousticEl = document.getElementById('calculated_sigma_acoustic');
        const selectedSigmaSurfaceEl = document.getElementById('selected_sigma_surface');

        // Results
        const resultSigmaTotalEl = document.getElementById('result_sigma_total');
        const resultThu95El = document.getElementById('result_thu_95');
        const chartContainerEl = document.getElementById('errorChart');

        const errorSourcesConfigForChart = [
            { id: 'ins_derived', label: 'INS Performance (HG9900)', color: '#EF4444' }, // red-500
            { id: 'acoustic_derived', label: 'Acoustic Fix (HiPAP)', color: '#F97316' }, // orange-500
            { id: 'surface_derived', label: 'Surface GNSS Fix', color: '#84CC16' }, // lime-500
            { id: 'gnss_offset_direct', label: 'GNSS Offset Error', color: '#F59E0B' }, // amber-500
            { id: 'ss_direct', label: 'Sound Speed (Valeport)', color: '#22C55E' }, // green-500
            { id: 'deploy_direct', label: 'Deployment/Cal.', color: '#14B8A6' }, // teal-500
            { id: 'other_direct', label: 'Other Unmodeled', color: '#6366F1' }  // indigo-500
        ];


        // --- Event Listeners ---
        function setupEventListeners() {
            insModeSelect.addEventListener('change', handleInsModeChange);
            
            [timeUnaidedInsMinutesSlider, timeDvlAidedInsMinutesSlider, auvSpeedKnotsSlider, 
             hipapSlantRangeSlider, sigmaGnssOffsetSlider, sigmaSsSlider, sigmaDeploySlider, sigmaOtherSlider]
            .forEach(slider => {
                if (slider) slider.addEventListener('input', handleSliderInputChange);
            });
            surfaceGpsQualitySelect.addEventListener('change', handleSelectInputChange);
            ihoOrderSelect.addEventListener('change', handleSelectInputChange); // Also recalculate on IHO order change
        }

        function handleInsModeChange() {
            const selectedMode = insModeSelect.value;
            if (selectedMode === 'unaided') {
                unaidedInsControls.classList.remove('hidden');
                dvlAidedInsControls.classList.add('hidden');
            } else { // dvl_aided
                unaidedInsControls.classList.add('hidden');
                dvlAidedInsControls.classList.remove('hidden');
            }
            calculateAndUpdateAll();
        }
        
        function handleSliderInputChange(event) {
            const sliderId = event.target.id;
            if (sliderValueDisplays[sliderId]) {
                 const numValue = parseFloat(event.target.value);
                 const step = parseFloat(event.target.step);
                 const decimals = step.toString().includes('.') ? step.toString().split('.')[1].length : 0;
                 sliderValueDisplays[sliderId].textContent = numValue.toFixed(decimals);
            }
            calculateAndUpdateAll();
        }

        function handleSelectInputChange() {
             calculateAndUpdateAll();
        }

        // --- Calculation Logic ---
        function calculateSigmaIns() {
            const mode = insModeSelect.value;
            if (mode === 'unaided') {
                const timeMinutes = parseFloat(timeUnaidedInsMinutesSlider.value);
                const timeHours = timeMinutes / 60.0;
                return timeHours * HG9900_UNAIDED_DRIFT_RATE_M_PER_SIGMA_HR;
            } else { // dvl_aided
                const timeMinutes = parseFloat(timeDvlAidedInsMinutesSlider.value);
                const timeHours = timeMinutes / 60.0;
                const speedKnots = parseFloat(auvSpeedKnotsSlider.value);
                const speedMps = speedKnots * KNOTS_TO_MPS;
                const distanceTravelledM = speedMps * timeHours * 3600; // timeHours * 3600 = seconds
                return distanceTravelledM * HG9900_DVL_AIDED_DRIFT_PERCENT_DT;
            }
        }

        function calculateSigmaAcoustic() {
            const slantRangeM = parseFloat(hipapSlantRangeSlider.value);
            const sigma = slantRangeM * HIPAP_ACCURACY_PERCENT_SLANT_RANGE;
            calculatedSigmaAcousticEl.textContent = `${sigma.toFixed(2)} m`;
            return sigma;
        }

        function getSigmaSurface() {
            const sigma = parseFloat(surfaceGpsQualitySelect.value);
            selectedSigmaSurfaceEl.textContent = `${sigma.toFixed(2)} m`;
            return sigma;
        }
        
        function getSigmaGnssOffset() {
            return parseFloat(sigmaGnssOffsetSlider.value);
        }

        function updateIHOCompliance(calculatedTHU) {
            const selectedOrder = ihoOrderSelect.value;
            const limit = IHO_S44_LIMITS[selectedOrder];
            
            ihoLimitDisplay.textContent = limit === Infinity ? "N/A" : `${limit.toFixed(2)} m`;

            if (selectedOrder === 'none') {
                ihoStatusDisplay.textContent = "NOT CHECKED";
                ihoStatusDisplay.className = 'iho-status-note';
            } else if (calculatedTHU <= limit) {
                ihoStatusDisplay.textContent = "PASS";
                ihoStatusDisplay.className = 'iho-status-pass';
            } else {
                ihoStatusDisplay.textContent = "FAIL";
                ihoStatusDisplay.className = 'iho-status-fail';
            }
        }


        function calculateAndUpdateAll() {
            // Update slider displays
            for (const id in sliderValueDisplays) {
                const slider = document.getElementById(id);
                if (slider && sliderValueDisplays[id]) {
                     const numValue = parseFloat(slider.value);
                     const step = parseFloat(slider.step);
                     const decimals = step.toString().includes('.') ? step.toString().split('.')[1].length : 0;
                     sliderValueDisplays[id].textContent = numValue.toFixed(decimals);
                }
            }

            const sigmaValues = {
                ins_derived: calculateSigmaIns(),
                acoustic_derived: calculateSigmaAcoustic(),
                surface_derived: getSigmaSurface(),
                gnss_offset_direct: getSigmaGnssOffset(),
                ss_direct: parseFloat(sigmaSsSlider.value),
                deploy_direct: parseFloat(sigmaDeploySlider.value),
                other_direct: parseFloat(sigmaOtherSlider.value)
            };

            let sumOfSquares = 0;
            const chartData = [];

            errorSourcesConfigForChart.forEach(config => {
                const value = sigmaValues[config.id];
                let variance = 0;
                if (!isNaN(value) && value >= 0) {
                    variance = value * value;
                    sumOfSquares += variance;
                }
                chartData.push({
                    label: config.label,
                    variance: variance,
                    value: isNaN(value) || value < 0 ? 0 : value,
                    color: config.color
                });
            });
            
            const sigmaHTotal = Math.sqrt(sumOfSquares);
            const thu95 = 2 * sigmaHTotal;

            resultSigmaTotalEl.innerHTML = `${sigmaHTotal.toFixed(2)} <span class="unit-text-result">m</span>`;
            resultThu95El.innerHTML = `${thu95.toFixed(2)} <span class="unit-text-result">m</span>`;

            updateChart(chartData, sumOfSquares);
            updateIHOCompliance(thu95);
        }

        function updateChart(variances, totalVariance) {
            chartContainerEl.innerHTML = ''; 
            if (totalVariance === 0) { 
                 variances.forEach(item => {
                    const barGroup = document.createElement('div');
                    barGroup.className = 'chart-bar-group';
                    barGroup.innerHTML = `
                        <span class="chart-bar-label" title="${item.label} (σ = ${item.value.toFixed(2)} m)">${item.label}:</span>
                        <div class="chart-bar" style="width: 0%; background-color: ${item.color};"></div>
                        <span class="chart-bar-value">${item.value.toFixed(2)} m (0.0%)</span>
                    `;
                    chartContainerEl.appendChild(barGroup);
                });
                return;
            }

            variances.forEach(item => {
                const percentageOfTotalVariance = totalVariance > 0 ? (item.variance / totalVariance) * 100 : 0;
                const barWidth = percentageOfTotalVariance; 

                const barGroup = document.createElement('div');
                barGroup.className = 'chart-bar-group';
                const labelEl = document.createElement('span');
                labelEl.className = 'chart-bar-label';
                labelEl.title = `${item.label} (σ = ${item.value.toFixed(2)} m)`;
                labelEl.textContent = `${item.label}:`;
                const barEl = document.createElement('div');
                barEl.className = 'chart-bar';
                barEl.style.width = `${barWidth.toFixed(2)}%`;
                barEl.style.backgroundColor = item.color;
                barEl.title = `Variance (σ²): ${item.variance.toFixed(2)} m², Contribution: ${percentageOfTotalVariance.toFixed(1)}%`;
                const valueEl = document.createElement('span');
                valueEl.className = 'chart-bar-value';
                valueEl.textContent = `${item.value.toFixed(2)} m (${percentageOfTotalVariance.toFixed(1)}%)`;
                barGroup.appendChild(labelEl);
                barGroup.appendChild(barEl);
                barGroup.appendChild(valueEl);
                chartContainerEl.appendChild(barGroup);
            });
        }

        // --- Initial Setup ---
        window.addEventListener('load', () => {
            setupEventListeners();
            handleInsModeChange(); 
            calculateAndUpdateAll(); 
        });
    </script>

</body>
</html>
