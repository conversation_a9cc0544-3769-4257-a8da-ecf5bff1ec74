<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fleet Overview & Mission Control</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'bridge-dark': '#0F172A',
                        'bridge-panel': '#1E293B',
                        'bridge-accent': '#38BDF8',
                        'bridge-success': '#10B981',
                        'bridge-warning': '#F59E0B',
                        'bridge-danger': '#EF4444',
                        'bridge-text': '#E2E8F0',
                        'bridge-text-dim': '#94A3B8'
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-bridge-dark text-bridge-text font-['Inter'] flex flex-col h-screen">
    <div class="bg-bridge-panel border-b border-slate-700 py-3 px-4 shadow-md">
        <div class="flex items-center justify-between">
            <h1 class="text-xl font-semibold flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 text-bridge-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0" />
                </svg>
                Remote Operations - Fleet Overview
            </h1>
            <div class="flex items-center space-x-4">
                <span class="text-bridge-text-dim text-sm">UTC: <span class="text-bridge-text" id="utc-time">12:45:32</span></span>
                <button class="bg-bridge-panel hover:bg-slate-700 text-bridge-text px-3 py-1 rounded border border-slate-600 text-sm flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    Settings
                </button>
            </div>
        </div>
    </div>
    <div class="flex flex-1 overflow-hidden">
        <!-- Left Panel -->
        <div class="w-1/5 bg-bridge-panel border-r border-slate-700 p-4 overflow-y-auto">
            <div class="text-sm font-semibold uppercase tracking-wider text-bridge-text-dim mb-3 pb-2 border-b border-slate-700">Fleet Status</div>
            
            <div class="bg-slate-800 rounded-md mb-3 overflow-hidden border-l-4 border-bridge-success hover:bg-slate-700 transition cursor-pointer">
                <div class="p-3">
                    <div class="flex justify-between items-start">
                        <div class="font-medium">Vessel Alpha</div>
                        <span class="bg-bridge-success/20 text-bridge-success text-xs px-2 py-0.5 rounded-full">Active</span>
                    </div>
                    <div class="mt-2 text-sm text-bridge-text-dim">
                        <div class="flex items-center mb-1">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                            </svg>
                            Surveying Line 102
                        </div>
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            No Alerts
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-slate-800 rounded-md mb-3 overflow-hidden border-l-4 border-bridge-warning hover:bg-slate-700 transition cursor-pointer">
                <div class="p-3">
                    <div class="flex justify-between items-start">
                        <div class="font-medium">Vessel Bravo</div>
                        <span class="bg-bridge-warning/20 text-bridge-warning text-xs px-2 py-0.5 rounded-full">Warning</span>
                    </div>
                    <div class="mt-2 text-sm text-bridge-text-dim">
                        <div class="flex items-center mb-1">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                            </svg>
                            MMO Active
                        </div>
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1.5 text-bridge-warning" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                            Minor Sensor Anomaly
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-slate-800 rounded-md mb-3 overflow-hidden border-l-4 border-bridge-success hover:bg-slate-700 transition cursor-pointer">
                <div class="p-3">
                    <div class="flex justify-between items-start">
                        <div class="font-medium">Vessel Charlie</div>
                        <span class="bg-bridge-success/20 text-bridge-success text-xs px-2 py-0.5 rounded-full">Active</span>
                    </div>
                    <div class="mt-2 text-sm text-bridge-text-dim">
                        <div class="flex items-center mb-1">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                            </svg>
                            AUV Deploying
                        </div>
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            No Alerts
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-slate-800 rounded-md mb-3 overflow-hidden border-l-4 border-bridge-danger hover:bg-slate-700 transition cursor-pointer">
                <div class="p-3">
                    <div class="flex justify-between items-start">
                        <div class="font-medium">Vessel Delta</div>
                        <span class="bg-bridge-danger/20 text-bridge-danger text-xs px-2 py-0.5 rounded-full">Critical</span>
                    </div>
                    <div class="mt-2 text-sm text-bridge-text-dim">
                        <div class="flex items-center mb-1">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                            </svg>
                            Comms Offline
                        </div>
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1.5 text-bridge-danger" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                            Critical Comms Fault
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-6">
                <div class="text-sm font-semibold uppercase tracking-wider text-bridge-text-dim mb-3 pb-2 border-b border-slate-700">Global Alerts</div>
                <div class="bg-bridge-danger/10 border border-bridge-danger/30 rounded-md p-3 text-sm">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-bridge-danger" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M
