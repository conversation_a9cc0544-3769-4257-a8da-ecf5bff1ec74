<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vessel Bravo - Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'bridge-dark': '#0F172A',
                        'bridge-panel': '#1E293B',
                        'bridge-accent': '#38BDF8',
                        'bridge-success': '#10B981',
                        'bridge-warning': '#F59E0B',
                        'bridge-danger': '#EF4444',
                        'bridge-text': '#E2E8F0',
                        'bridge-text-dim': '#94A3B8'
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; margin: 0; background-color: #0F172A; color: #E2E8F0; display: flex; flex-direction: column; height: 100vh; }
        .header { background-color: #1E293B; padding: 10px 20px; border-bottom: 1px solid #334155; display: flex; justify-content: space-between; align-items: center; }
        .header h1 { margin: 0; font-size: 1.5em; }
        .header-info { font-size: 0.9em; text-align: right;}
        .tab-container { background-color: #1E293B; padding: 0 10px; border-bottom: 1px solid #334155;}
        .tab-button { background-color: #334155; border: none; color: #E2E8F0; padding: 10px 15px; cursor: pointer; margin-right: 5px; border-top-left-radius: 4px; border-top-right-radius: 4px;}
        .tab-button.active { background-color: #38BDF8; color: #0F172A; font-weight: 500; }
        .tab-content { flex: 1; padding: 20px; background-color: #0F172A; overflow-y: auto; }
        .tab-pane { display: none; } /* Hidden by default */
        .tab-pane.active { display: block; }
        .grid-container { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; }
        .widget { background-color: #1E293B; padding: 15px; border-radius: 6px; border: 1px solid #334155; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); }
        .widget h3 { margin-top: 0; font-size: 1.1em; border-bottom: 1px solid #334155; padding-bottom: 8px; color: #38BDF8;}
        .widget p { margin: 8px 0; font-size: 0.9em; }
        .mini-map-container { height: 200px; background-color: #0F172A; border: 1px solid #334155; margin-bottom:10px; overflow: hidden; border-radius: 4px; position: relative;}
        .mini-map-image { width: 100%; height: 100%; object-fit: cover; }
        .mini-map-overlay { position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(15, 23, 42, 0.3); }
        .sensor-health-layout { display: flex; flex-wrap: wrap; gap: 10px; }
        .sensor-pod { width: 120px; height: 80px; background-color: #334155; border-radius: 6px; display: flex; flex-direction: column; align-items: center; justify-content: center; text-align: center; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);}
        .sensor-ok { border: 2px solid #10B981; }
        .sensor-warn { border: 2px solid #F59E0B; }
        .sensor-error { border: 2px solid #EF4444; }
        .file-list li { background-color: #334155; padding: 8px 10px; margin-bottom: 5px; border-radius: 4px; font-size: 0.85em;}
        .video-feed-container { height: 250px; background-color: #0F172A; border: 1px solid #334155; margin-bottom: 10px; overflow: hidden; border-radius: 4px; position: relative;}
        .video-feed-image { width: 100%; height: 100%; object-fit: cover; }
        .video-feed-overlay { position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(15, 23, 42, 0.3); }
        .button-primary { background-color: #38BDF8; color: #0F172A; border: none; padding: 8px 12px; border-radius: 4px; font-weight: 500; cursor: pointer; transition: all 0.2s; }
        .button-primary:hover { background-color: #0EA5E9; }
        .button-secondary { background-color: #334155; color: #E2E8F0; border: none; padding: 8px 12px; border-radius: 4px; font-weight: 500; cursor: pointer; transition: all 0.2s; }
        .button-secondary:hover { background-color: #475569; }
    </style>
</head>
<body>
    <div class="header">
        <h1 class="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 text-bridge-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0" />
            </svg>
            Vessel Bravo - Dashboard
        </h1>
        <div class="header-info">
            <div class="flex items-center justify-end mb-1">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-bridge-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <span>Lat: 12.3456 N, Lon: -78.9101 W</span>
            </div>
            <div class="flex items-center justify-end mb-1">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-bridge-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                <span>Speed: 8.2 kts, Heading: 125°</span>
            </div>
            <div class="flex items-center justify-end">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-bridge-warning" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <span class="text-bridge-warning">MMO Active - Minor Sensor Anomaly</span>
            </div>
        </div>
    </div>
    <div class="tab-container">
        <button class="tab-button active" onclick="openTab('operations')">Operations</button>
        <button class="tab-button" onclick="openTab('sensorHealth')">Sensor Health</button>
        <button class="tab-button" onclick="openTab('dataMgmt')">Data Management</button>
        <button class="tab-button" onclick="openTab('mmoView')">MMO View</button>
        <button class="tab-button" onclick="openTab('auvRov')">AUV/ROV</button>
    </div>

    <div class="tab-content">
        <div id="operations" class="tab-pane active">
            <div class="grid-container">
                <div class="widget">
                    <h3 class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                        </svg>
                        Live Survey
                    </h3>
                    <div class="mini-map-container">
                        <img src="https://i.imgur.com/JGQzXKY.jpg" alt="Navigation Map" class="mini-map-image">
                        <div class="mini-map-overlay"></div>
                    </div>
                    <p class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-bridge-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                        Current Line: <span class="ml-1 font-medium">LB1003_E</span>
                    </p>
                    <p class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-bridge-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                        </svg>
                        Next Line: <span class="ml-1 font-medium">LB1004_W</span>
                    </p>
                    <p class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-bridge-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        ETA Next Line: <span class="ml-1 font-medium">15 min</span>
                    </p>
                </div>
                <div class="widget">
                    <h3 class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                        </svg>
                        Key Sensor Data
                    </h3>
                    <p class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-bridge-success" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        MBES Status: <span class="ml-1 font-medium">Pinging</span>
                    </p>
                    <p class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-bridge-success" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        INS Heading: <span class="ml-1 font-medium">125.1° (RMS 0.02°)</span>
                    </p>
                    <p class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-bridge-success" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        GNSS Mode: <span class="ml-1 font-medium">APEX2 (HDOP 0.9)</span>
                    </p>
                    <p class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-bridge-success" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        SVS (Surface): <span class="ml-1 font-medium">1501.2 m/s</span>
                    </p>
                </div>
                <div class="widget">
                    <h3 class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 01-2 2H6a2 2 0 01-2-2z" />
                        </svg>
                        SVP Status
                    </h3>
                    <p class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-bridge-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Last Cast: <span class="ml-1 font-medium">2 hours ago</span>
                    </p>
                    <p class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-bridge-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Surface SV Delta: <span class="ml-1 font-medium">0.8 m/s</span>
                    </p>
                    <p class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-bridge-warning" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                        Suggestion: <span class="ml-1 font-medium">Consider new cast soon.</span>
                    </p>
                    <button class="button-primary w-full" onclick="initiateSVP()">Initiate SVP Cast</button>
                </div>
                 <div class="widget">
                    <h3 class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 01-2 2H6a2 2 0 01-2-2z" />
                        </svg>
                        MMO / PAM Status
                    </h3>
                    <p class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-bridge-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        MMO Observer: <span class="ml-1 font-medium">J. Doe (Ashore)</span>
                    </p>
                    <p class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-bridge-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Visual Detections (last hr): <span class="ml-1 font-medium">2</span>
                    </p>
                    <p class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-bridge-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Acoustic Detections (last hr): <span class="ml-1 font-medium">5 (Possible Dolphins)</span>
                    </p>
                </div>
            </div>
        </div>

        <div id="sensorHealth" class="tab-pane">
            <h3>Sensor Health Overview (P&ID Style Layout Placeholder)</h3>
            <div class="sensor-health-layout">
                <div class="sensor-pod sensor-ok">INS Sprint 500<br><small>OK</small></div>
                <div class="sensor-pod sensor-ok">Veripos LD900 #1<br><small>OK</small></div>
                <div class="sensor-pod sensor-warn">Veripos LD900 #2<br><small>WARN - Low SNR</small></div>
                <div class="sensor-pod sensor-ok">EM2040 MBES<br><small>OK</small></div>
                <div class="sensor-pod sensor-ok">HiPAP 501<br><small>OK</small></div>
                <div class="sensor-pod sensor-ok">Valeport SVS<br><small>OK</small></div>
                <div class="sensor-pod sensor-error">Weather Station<br><small>ERROR - No Data</small></div>
                <!-- Add more sensor pods -->
            </div>
            <p style="margin-top:15px;"><small>Click on a sensor for detailed decoded/raw data, AI insights, and historical trends.</small></p>
        </div>

        <div id="dataMgmt" class="tab-pane">
            <div class="grid-container">
                <div class="widget">
                    <h3>On-Vessel Raw Data Files</h3>
                    <ul class="file-list">
                        <li>EM2040_20231027_140000.all (2.1 GB)</li>
                        <li>INS_20231027_140000.log (150 MB)</li>
                        <li>SVS_Cast_20231027_133000.svp (5 KB)</li>
                        <!-- More files -->
                    </ul>
                </div>
                <div class="widget">
                    <h3>FileCatalyst Transfers</h3>
                    <p>Queue: 3 files pending</p>
                    <p>Current Transfer: EM2040_20231027_120000.all (65% done)</p>
                    <button style="width:100%; padding:8px; background-color:#5dade2; border:none; color:white;">Request Specific File Transfer</button>
                </div>
            </div>
        </div>
        
        <div id="mmoView" class="tab-pane">
            <h3>MMO Optical & Acoustic View</h3>
            <div class="grid-container" style="grid-template-columns: 2fr 1fr;">
                <div class="widget">
                    <h3>Optical Radar (360° Camera Feeds Placeholder)</h3>
                    <div class="video-feed-placeholder" style="height: 400px;">(Multiple Video Streams with AI Detection Overlays)</div>
                </div>
                <div class="widget">
                    <h3>PAM Spectrograms & Alerts</h3>
                    <div class="video-feed-placeholder" style="height: 150px;">(Spectrogram 1 - Hydrophone Array Port)</div>
                    <div class="video-feed-placeholder" style="height: 150px;">(Spectrogram 2 - Hydrophone Array Stbd)</div>
                    <p style="margin-top:10px;">Acoustic Alert: Possible Dolphin Clicks - Bearing 045°</p>
                </div>
            </div>
             <div class="widget" style="margin-top:15px;">
                <h3>MMO Event Log & Reporting</h3>
                <p>Manual Sighting: 14:15 UTC - 3 Dolphins - Behaviour: Travelling - Range: 800m</p>
                <button style="padding:8px; background-color:#5dade2; border:none; color:white;">Add Manual Sighting</button>
                <button style="padding:8px; background-color:#27ae60; border:none; color:white;">Generate MMO Report</button>
            </div>
        </div>

        <div id="auvRov" class="tab-pane">
            <h3>AUV/ROV Control & Monitoring</h3>
            <p>AUV HUGIN_01: Deployed - Mission "Pipeline_Inspection_N01"</p>
            <p>Status: Executing Line 5 - Battery 75%</p>
            <p>ROV Leopard_01: On Deck - Ready</p>
            <div class="widget" style="margin-top:15px;">
                <button style="padding:8px; background-color:#5dade2; border:none; color:white;">View AUV Mission Plan</button>
                <button style="padding:8px; background-color:#e67e22; border:none; color:white;">Send AUV Command</button>
            </div>
        </div>
    </div>

    <script>
        function openTab(tabName) {
            var i, tabcontent, tablinks;
            tabcontent = document.getElementsByClassName("tab-pane");
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].style.display = "none";
                tabcontent[i].classList.remove("active");
            }
            tablinks = document.getElementsByClassName("tab-button");
            for (i = 0; i < tablinks.length; i++) {
                tablinks[i].classList.remove("active");
            }
            document.getElementById(tabName).style.display = "block";
            document.getElementById(tabName).classList.add("active");
            // Find the button that controls this tab and set it to active
            for (i = 0; i < tablinks.length; i++) {
                if(tablinks[i].getAttribute('onclick') === "openTab('"+tabName+"')") {
                    tablinks[i].classList.add("active");
                }
            }
        }
        // Call openTab for the default active tab after page load
        document.addEventListener('DOMContentLoaded', function() {
            openTab('operations');
        });
    </script>
</body>
</html>
