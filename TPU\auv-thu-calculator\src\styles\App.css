/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f9fafb;
  color: #111827;
  line-height: 1.6;
}

/* Container styles */
.app-container {
  min-height: 100vh;
  padding: 1rem;
}

.calculator-container {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 2rem;
  background-color: #ffffff;
  border-radius: 0.75rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Header styles */
.header {
  text-align: center;
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 2px solid #e5e7eb;
}

.header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
}

.header p {
  font-size: 1.125rem;
  color: #6b7280;
  max-width: 800px;
  margin: 0 auto;
}

.company-logos {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.company-logo {
  height: 60px;
  width: auto;
  object-fit: contain;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.company-logo:hover {
  opacity: 1;
}

/* Grid layout */
.main-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin-bottom: 3rem;
}

@media (max-width: 1024px) {
  .main-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

/* Section styles */
.section {
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

/* Input group styles */
.input-group {
  margin-bottom: 1.5rem;
}

.input-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #374151;
  font-weight: 500;
  font-size: 0.875rem;
}

.input-description {
  font-size: 0.8rem;
  color: #6b7280;
  margin-bottom: 0.75rem;
  line-height: 1.4;
}

/* Input field styles */
.input-field-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.input-field,
.select-field {
  flex-grow: 1;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  transition: all 0.2s ease-in-out;
  background-color: #ffffff;
}

.input-field:focus,
.select-field:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

/* Slider styles */
.input-slider {
  flex-grow: 1;
  height: 6px;
  border-radius: 3px;
  background: #e5e7eb;
  outline: none;
  cursor: pointer;
  transition: background 0.3s ease;
}

.input-slider::-webkit-slider-thumb {
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.input-slider::-webkit-slider-thumb:hover {
  background: #2563eb;
  transform: scale(1.1);
}

.input-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slider-value-display {
  min-width: 70px;
  text-align: right;
  font-weight: 500;
  color: #1f2937;
  font-size: 0.875rem;
}

.unit-text-input {
  font-size: 0.875rem;
  color: #4b5563;
  font-weight: 500;
}

/* Results card styles */
.results-card {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  padding: 2rem;
  border-radius: 0.75rem;
  margin-top: 2rem;
  border: 1px solid #d1d5db;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.result-item h3 {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 0.5rem;
}

.result-value {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
}

.unit-text-result {
  font-size: 1rem;
  color: #4b5563;
  font-weight: 400;
}

.info-text {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 1rem;
  padding: 1rem;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 0.5rem;
  border-left: 4px solid #3b82f6;
}

/* IHO compliance styles */
.iho-status-pass {
  color: #16a34a;
  font-weight: bold;
}

.iho-status-fail {
  color: #dc2626;
  font-weight: bold;
}

.iho-status-note {
  color: #f59e0b;
  font-weight: bold;
}

/* Chart styles */
.chart-container {
  margin-top: 2rem;
  padding: 1.5rem;
  background-color: #f9fafb;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
}

.chart-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
}

.chart-bar-group {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
  min-height: 32px;
}

.chart-bar-label {
  width: 220px;
  flex-shrink: 0;
  color: #4b5563;
  padding-right: 0.75rem;
  text-align: right;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}

.chart-bar {
  height: 24px;
  border-radius: 0.25rem;
  transition: all 0.3s ease-in-out;
  min-width: 2px;
  position: relative;
  overflow: hidden;
}

.chart-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.chart-bar-value {
  margin-left: 0.75rem;
  color: #1f2937;
  font-weight: 500;
  min-width: 120px;
}

/* Footer styles */
.footer {
  margin-top: 3rem;
  padding: 2rem 0;
  text-align: center;
  border-top: 1px solid #e5e7eb;
}

.footer p {
  font-size: 0.75rem;
  color: #6b7280;
  max-width: 800px;
  margin: 0 auto;
}

/* Utility classes */
.hidden {
  display: none;
}

/* Responsive design */
@media (max-width: 768px) {
  .calculator-container {
    margin: 1rem auto;
    padding: 1rem;
  }
  
  .header h1 {
    font-size: 2rem;
  }
  
  .header p {
    font-size: 1rem;
  }
  
  .company-logos {
    gap: 1rem;
  }
  
  .company-logo {
    height: 40px;
  }
  
  .chart-bar-label {
    width: 150px;
    font-size: 0.75rem;
  }
  
  .result-value {
    font-size: 1.5rem;
  }
}

/* Tab Navigation */
.tab-navigation {
  display: flex;
  margin-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.tab-button {
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  color: #6b7280;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  cursor: pointer;
}

.tab-button.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
}

/* Calculation Details Styles */
.calculation-details {
  padding: 1.5rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.detail-section {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.detail-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
}

.formula {
  font-family: 'Courier New', monospace;
  background-color: #f3f4f6;
  padding: 0.5rem;
  border-radius: 0.25rem;
  margin-bottom: 0.5rem;
}

.calculation {
  margin-bottom: 0.5rem;
  line-height: 1.6;
}

.explanation {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.5rem;
}

