{"ast": null, "code": "// Configuration Constants (User should verify/adjust these for their specific setup)\nexport const HG9900_UNAIDED_DRIFT_RATE_M_PER_SIGMA_HR = 75.0; // Illustrative: 1-sigma radial error growth (m/hr)\nexport const HG9900_DVL_AIDED_DRIFT_PERCENT_DT = 0.001; // Illustrative: 0.1% of Distance Travelled (1-sigma)\nexport const HIPAP_ACCURACY_PERCENT_SLANT_RANGE = 0.002; // Illustrative: 0.2% of Slant Range (1-sigma)\nexport const KNOTS_TO_MPS = 0.514444;\n\n// Additional INS constants\nexport const HG9900_HEADING_ACCURACY_DEG = 0.05; // 0.05° heading accuracy (1σ)\nexport const HG9900_PITCH_ROLL_ACCURACY_DEG = 0.02; // 0.02° pitch/roll accuracy (1σ)\n\n// Sound velocity error constants\nexport const SVP_MEASUREMENT_ERROR_M_S = 0.02; // 0.02 m/s measurement error (1σ)\nexport const SVP_SPATIAL_TEMPORAL_ERROR_M_S = 2.0; // 2.0 m/s spatial/temporal variation (1σ)\n\n// Lever arm measurement constants\nexport const LEVER_ARM_MEASUREMENT_ERROR_M = 0.01; // 1cm measurement error (1σ)\n\nexport const IHO_S44_LIMITS = {\n  // THU95% limits in meters\n  exclusive: 0.5,\n  special: 1.0,\n  '1a': 2.0,\n  '1b': 5.0,\n  '2a': 10.0,\n  '2b': 20.0,\n  none: Infinity\n};\nexport const ERROR_SOURCES_CONFIG = [{\n  id: 'ins_derived',\n  label: 'INS Performance (HG9900)',\n  color: '#EF4444'\n},\n// red-500\n{\n  id: 'acoustic_derived',\n  label: 'Acoustic Fix (HiPAP)',\n  color: '#F97316'\n},\n// orange-500\n{\n  id: 'surface_derived',\n  label: 'Surface GNSS Fix',\n  color: '#84CC16'\n},\n// lime-500\n{\n  id: 'gnss_offset_direct',\n  label: 'GNSS Offset Error',\n  color: '#F59E0B'\n},\n// amber-500\n{\n  id: 'ss_direct',\n  label: 'Sound Speed (Valeport)',\n  color: '#22C55E'\n},\n// green-500\n{\n  id: 'deploy_direct',\n  label: 'Deployment/Cal.',\n  color: '#14B8A6'\n},\n// teal-500\n{\n  id: 'other_direct',\n  label: 'Other Unmodeled',\n  color: '#6366F1'\n} // indigo-500\n];\n\n// Expanded error sources configuration\nexport const EXPANDED_ERROR_SOURCES_CONFIG = [...ERROR_SOURCES_CONFIG, {\n  id: 'lever_arm_error',\n  label: 'Lever Arm Error',\n  color: '#8e44ad'\n}, {\n  id: 'svp_error',\n  label: 'Sound Velocity Error',\n  color: '#d35400'\n}, {\n  id: 'attitude_error',\n  label: 'Attitude Error',\n  color: '#27ae60'\n}];\nexport const GNSS_QUALITY_OPTIONS = [{\n  value: '2.5',\n  label: 'Uncorrected GNSS (σ ≈ 2.5m)'\n}, {\n  value: '0.5',\n  label: 'Differential GNSS (DGPS/RTK Float) (σ ≈ 0.5m)'\n}, {\n  value: '0.05',\n  label: 'Veripos Apex/Ultra (PPP/RTK Fixed) (σ ≈ 0.05m)'\n}];\nexport const IHO_ORDER_OPTIONS = [{\n  value: 'exclusive',\n  label: 'Exclusive Order (0.5m)'\n}, {\n  value: 'special',\n  label: 'Special Order (1.0m)'\n}, {\n  value: '1a',\n  label: 'Order 1a (2.0m)'\n}, {\n  value: '1b',\n  label: 'Order 1b (5.0m)'\n}, {\n  value: '2a',\n  label: 'Order 2a (10.0m)'\n}, {\n  value: '2b',\n  label: 'Order 2b (20.0m)'\n}, {\n  value: 'none',\n  label: 'None (No Check)'\n}];\n\n// IHO S-44 Ed. 6.0.0 constants (expanded)\nexport const IHO_S44_DEPTH_DEPENDENT = {\n  'exclusive': {\n    a: 0.5,\n    b: 0.0075\n  },\n  // a + b*d\n  'special': {\n    a: 1.0,\n    b: 0.015\n  },\n  '1a': {\n    a: 2.0,\n    b: 0.023\n  },\n  '1b': {\n    a: 5.0,\n    b: 0.05\n  },\n  '2a': {\n    a: 10.0,\n    b: 0.05\n  },\n  '2b': {\n    a: 20.0,\n    b: 0.10\n  },\n  'none': {\n    a: Infinity,\n    b: 0\n  }\n};\n\n// Company logos (using high-quality placeholder URLs that can be easily replaced)\nexport const COMPANY_LOGOS = {\n  oceanInfinity: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=200&h=80&fit=crop&crop=center&auto=format&q=80',\n  honeywell: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=150&h=60&fit=crop&crop=center&auto=format&q=80',\n  kongsberg: 'https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=150&h=60&fit=crop&crop=center&auto=format&q=80',\n  valeport: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=150&h=60&fit=crop&crop=center&auto=format&q=80'\n};", "map": {"version": 3, "names": ["HG9900_UNAIDED_DRIFT_RATE_M_PER_SIGMA_HR", "HG9900_DVL_AIDED_DRIFT_PERCENT_DT", "HIPAP_ACCURACY_PERCENT_SLANT_RANGE", "KNOTS_TO_MPS", "HG9900_HEADING_ACCURACY_DEG", "HG9900_PITCH_ROLL_ACCURACY_DEG", "SVP_MEASUREMENT_ERROR_M_S", "SVP_SPATIAL_TEMPORAL_ERROR_M_S", "LEVER_ARM_MEASUREMENT_ERROR_M", "IHO_S44_LIMITS", "exclusive", "special", "none", "Infinity", "ERROR_SOURCES_CONFIG", "id", "label", "color", "EXPANDED_ERROR_SOURCES_CONFIG", "GNSS_QUALITY_OPTIONS", "value", "IHO_ORDER_OPTIONS", "IHO_S44_DEPTH_DEPENDENT", "a", "b", "COMPANY_LOGOS", "oceanInfinity", "honeywell", "kongsberg", "valeport"], "sources": ["C:/Dev/TPU/auv-thu-calculator/src/utils/constants.js"], "sourcesContent": ["// Configuration Constants (User should verify/adjust these for their specific setup)\nexport const HG9900_UNAIDED_DRIFT_RATE_M_PER_SIGMA_HR = 75.0; // Illustrative: 1-sigma radial error growth (m/hr)\nexport const HG9900_DVL_AIDED_DRIFT_PERCENT_DT = 0.001; // Illustrative: 0.1% of Distance Travelled (1-sigma)\nexport const HIPAP_ACCURACY_PERCENT_SLANT_RANGE = 0.002; // Illustrative: 0.2% of Slant Range (1-sigma)\nexport const KNOTS_TO_MPS = 0.514444;\n\n// Additional INS constants\nexport const HG9900_HEADING_ACCURACY_DEG = 0.05; // 0.05° heading accuracy (1σ)\nexport const HG9900_PITCH_ROLL_ACCURACY_DEG = 0.02; // 0.02° pitch/roll accuracy (1σ)\n\n// Sound velocity error constants\nexport const SVP_MEASUREMENT_ERROR_M_S = 0.02; // 0.02 m/s measurement error (1σ)\nexport const SVP_SPATIAL_TEMPORAL_ERROR_M_S = 2.0; // 2.0 m/s spatial/temporal variation (1σ)\n\n// Lever arm measurement constants\nexport const LEVER_ARM_MEASUREMENT_ERROR_M = 0.01; // 1cm measurement error (1σ)\n\nexport const IHO_S44_LIMITS = {\n  // THU95% limits in meters\n  exclusive: 0.5,\n  special: 1.0,\n  '1a': 2.0,\n  '1b': 5.0,\n  '2a': 10.0,\n  '2b': 20.0,\n  none: Infinity\n};\n\nexport const ERROR_SOURCES_CONFIG = [\n  { id: 'ins_derived', label: 'INS Performance (HG9900)', color: '#EF4444' }, // red-500\n  { id: 'acoustic_derived', label: 'Acoustic Fix (HiPAP)', color: '#F97316' }, // orange-500\n  { id: 'surface_derived', label: 'Surface GNSS Fix', color: '#84CC16' }, // lime-500\n  { id: 'gnss_offset_direct', label: 'GNSS Offset Error', color: '#F59E0B' }, // amber-500\n  { id: 'ss_direct', label: 'Sound Speed (Valeport)', color: '#22C55E' }, // green-500\n  { id: 'deploy_direct', label: 'Deployment/Cal.', color: '#14B8A6' }, // teal-500\n  { id: 'other_direct', label: 'Other Unmodeled', color: '#6366F1' } // indigo-500\n];\n\n// Expanded error sources configuration\nexport const EXPANDED_ERROR_SOURCES_CONFIG = [\n  ...ERROR_SOURCES_CONFIG,\n  {\n    id: 'lever_arm_error',\n    label: 'Lever Arm Error',\n    color: '#8e44ad'\n  },\n  {\n    id: 'svp_error',\n    label: 'Sound Velocity Error',\n    color: '#d35400'\n  },\n  {\n    id: 'attitude_error',\n    label: 'Attitude Error',\n    color: '#27ae60'\n  }\n];\n\nexport const GNSS_QUALITY_OPTIONS = [\n  { value: '2.5', label: 'Uncorrected GNSS (σ ≈ 2.5m)' },\n  { value: '0.5', label: 'Differential GNSS (DGPS/RTK Float) (σ ≈ 0.5m)' },\n  { value: '0.05', label: 'Veripos Apex/Ultra (PPP/RTK Fixed) (σ ≈ 0.05m)' }\n];\n\nexport const IHO_ORDER_OPTIONS = [\n  { value: 'exclusive', label: 'Exclusive Order (0.5m)' },\n  { value: 'special', label: 'Special Order (1.0m)' },\n  { value: '1a', label: 'Order 1a (2.0m)' },\n  { value: '1b', label: 'Order 1b (5.0m)' },\n  { value: '2a', label: 'Order 2a (10.0m)' },\n  { value: '2b', label: 'Order 2b (20.0m)' },\n  { value: 'none', label: 'None (No Check)' }\n];\n\n// IHO S-44 Ed. 6.0.0 constants (expanded)\nexport const IHO_S44_DEPTH_DEPENDENT = {\n  'exclusive': { a: 0.5, b: 0.0075 }, // a + b*d\n  'special': { a: 1.0, b: 0.015 },\n  '1a': { a: 2.0, b: 0.023 },\n  '1b': { a: 5.0, b: 0.05 },\n  '2a': { a: 10.0, b: 0.05 },\n  '2b': { a: 20.0, b: 0.10 },\n  'none': { a: Infinity, b: 0 }\n};\n\n// Company logos (using high-quality placeholder URLs that can be easily replaced)\nexport const COMPANY_LOGOS = {\n  oceanInfinity: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=200&h=80&fit=crop&crop=center&auto=format&q=80',\n  honeywell: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=150&h=60&fit=crop&crop=center&auto=format&q=80',\n  kongsberg: 'https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=150&h=60&fit=crop&crop=center&auto=format&q=80',\n  valeport: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=150&h=60&fit=crop&crop=center&auto=format&q=80'\n};\n\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,wCAAwC,GAAG,IAAI,CAAC,CAAC;AAC9D,OAAO,MAAMC,iCAAiC,GAAG,KAAK,CAAC,CAAC;AACxD,OAAO,MAAMC,kCAAkC,GAAG,KAAK,CAAC,CAAC;AACzD,OAAO,MAAMC,YAAY,GAAG,QAAQ;;AAEpC;AACA,OAAO,MAAMC,2BAA2B,GAAG,IAAI,CAAC,CAAC;AACjD,OAAO,MAAMC,8BAA8B,GAAG,IAAI,CAAC,CAAC;;AAEpD;AACA,OAAO,MAAMC,yBAAyB,GAAG,IAAI,CAAC,CAAC;AAC/C,OAAO,MAAMC,8BAA8B,GAAG,GAAG,CAAC,CAAC;;AAEnD;AACA,OAAO,MAAMC,6BAA6B,GAAG,IAAI,CAAC,CAAC;;AAEnD,OAAO,MAAMC,cAAc,GAAG;EAC5B;EACAC,SAAS,EAAE,GAAG;EACdC,OAAO,EAAE,GAAG;EACZ,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,IAAI;EACV,IAAI,EAAE,IAAI;EACVC,IAAI,EAAEC;AACR,CAAC;AAED,OAAO,MAAMC,oBAAoB,GAAG,CAClC;EAAEC,EAAE,EAAE,aAAa;EAAEC,KAAK,EAAE,0BAA0B;EAAEC,KAAK,EAAE;AAAU,CAAC;AAAE;AAC5E;EAAEF,EAAE,EAAE,kBAAkB;EAAEC,KAAK,EAAE,sBAAsB;EAAEC,KAAK,EAAE;AAAU,CAAC;AAAE;AAC7E;EAAEF,EAAE,EAAE,iBAAiB;EAAEC,KAAK,EAAE,kBAAkB;EAAEC,KAAK,EAAE;AAAU,CAAC;AAAE;AACxE;EAAEF,EAAE,EAAE,oBAAoB;EAAEC,KAAK,EAAE,mBAAmB;EAAEC,KAAK,EAAE;AAAU,CAAC;AAAE;AAC5E;EAAEF,EAAE,EAAE,WAAW;EAAEC,KAAK,EAAE,wBAAwB;EAAEC,KAAK,EAAE;AAAU,CAAC;AAAE;AACxE;EAAEF,EAAE,EAAE,eAAe;EAAEC,KAAK,EAAE,iBAAiB;EAAEC,KAAK,EAAE;AAAU,CAAC;AAAE;AACrE;EAAEF,EAAE,EAAE,cAAc;EAAEC,KAAK,EAAE,iBAAiB;EAAEC,KAAK,EAAE;AAAU,CAAC,CAAC;AAAA,CACpE;;AAED;AACA,OAAO,MAAMC,6BAA6B,GAAG,CAC3C,GAAGJ,oBAAoB,EACvB;EACEC,EAAE,EAAE,iBAAiB;EACrBC,KAAK,EAAE,iBAAiB;EACxBC,KAAK,EAAE;AACT,CAAC,EACD;EACEF,EAAE,EAAE,WAAW;EACfC,KAAK,EAAE,sBAAsB;EAC7BC,KAAK,EAAE;AACT,CAAC,EACD;EACEF,EAAE,EAAE,gBAAgB;EACpBC,KAAK,EAAE,gBAAgB;EACvBC,KAAK,EAAE;AACT,CAAC,CACF;AAED,OAAO,MAAME,oBAAoB,GAAG,CAClC;EAAEC,KAAK,EAAE,KAAK;EAAEJ,KAAK,EAAE;AAA8B,CAAC,EACtD;EAAEI,KAAK,EAAE,KAAK;EAAEJ,KAAK,EAAE;AAAgD,CAAC,EACxE;EAAEI,KAAK,EAAE,MAAM;EAAEJ,KAAK,EAAE;AAAiD,CAAC,CAC3E;AAED,OAAO,MAAMK,iBAAiB,GAAG,CAC/B;EAAED,KAAK,EAAE,WAAW;EAAEJ,KAAK,EAAE;AAAyB,CAAC,EACvD;EAAEI,KAAK,EAAE,SAAS;EAAEJ,KAAK,EAAE;AAAuB,CAAC,EACnD;EAAEI,KAAK,EAAE,IAAI;EAAEJ,KAAK,EAAE;AAAkB,CAAC,EACzC;EAAEI,KAAK,EAAE,IAAI;EAAEJ,KAAK,EAAE;AAAkB,CAAC,EACzC;EAAEI,KAAK,EAAE,IAAI;EAAEJ,KAAK,EAAE;AAAmB,CAAC,EAC1C;EAAEI,KAAK,EAAE,IAAI;EAAEJ,KAAK,EAAE;AAAmB,CAAC,EAC1C;EAAEI,KAAK,EAAE,MAAM;EAAEJ,KAAK,EAAE;AAAkB,CAAC,CAC5C;;AAED;AACA,OAAO,MAAMM,uBAAuB,GAAG;EACrC,WAAW,EAAE;IAAEC,CAAC,EAAE,GAAG;IAAEC,CAAC,EAAE;EAAO,CAAC;EAAE;EACpC,SAAS,EAAE;IAAED,CAAC,EAAE,GAAG;IAAEC,CAAC,EAAE;EAAM,CAAC;EAC/B,IAAI,EAAE;IAAED,CAAC,EAAE,GAAG;IAAEC,CAAC,EAAE;EAAM,CAAC;EAC1B,IAAI,EAAE;IAAED,CAAC,EAAE,GAAG;IAAEC,CAAC,EAAE;EAAK,CAAC;EACzB,IAAI,EAAE;IAAED,CAAC,EAAE,IAAI;IAAEC,CAAC,EAAE;EAAK,CAAC;EAC1B,IAAI,EAAE;IAAED,CAAC,EAAE,IAAI;IAAEC,CAAC,EAAE;EAAK,CAAC;EAC1B,MAAM,EAAE;IAAED,CAAC,EAAEV,QAAQ;IAAEW,CAAC,EAAE;EAAE;AAC9B,CAAC;;AAED;AACA,OAAO,MAAMC,aAAa,GAAG;EAC3BC,aAAa,EAAE,4GAA4G;EAC3HC,SAAS,EAAE,4GAA4G;EACvHC,SAAS,EAAE,+GAA+G;EAC1HC,QAAQ,EAAE;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}