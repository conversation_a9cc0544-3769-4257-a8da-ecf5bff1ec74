{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect, useCallback } from 'react';\nimport { HG9900_UNAIDED_DRIFT_RATE_M_PER_SIGMA_HR, HG9900_DVL_AIDED_DRIFT_PERCENT_DT, HIPAP_ACCURACY_PERCENT_SLANT_RANGE, KNOTS_TO_MPS, ERROR_SOURCES_CONFIG } from '../utils/constants';\nexport const useCalculations = () => {\n  _s();\n  // State for all input parameters\n  const [insMode, setInsMode] = useState('dvl_aided');\n  const [timeUnaidedInsMinutes, setTimeUnaidedInsMinutes] = useState(30);\n  const [timeDvlAidedInsMinutes, setTimeDvlAidedInsMinutes] = useState(30);\n  const [auvSpeedKnots, setAuvSpeedKnots] = useState(3.0);\n  const [hipapSlantRangeM, setHipapSlantRangeM] = useState(150);\n  const [surfaceGpsQuality, setSurfaceGpsQuality] = useState('2.5');\n  const [sigmaGnssOffset, setSigmaGnssOffset] = useState(0.02);\n  const [sigmaSs, setSigmaSs] = useState(0.5);\n  const [sigmaDeploy, setSigmaDeploy] = useState(1.0);\n  const [sigmaOther, setSigmaOther] = useState(0.25);\n\n  // State for calculated results\n  const [sigmaValues, setSigmaValues] = useState({});\n  const [sigmaHTotal, setSigmaHTotal] = useState(0);\n  const [thu95, setThu95] = useState(0);\n  const [chartData, setChartData] = useState([]);\n\n  // Calculate INS sigma based on mode\n  const calculateSigmaIns = useCallback(() => {\n    if (insMode === 'unaided') {\n      const timeHours = timeUnaidedInsMinutes / 60.0;\n      return timeHours * HG9900_UNAIDED_DRIFT_RATE_M_PER_SIGMA_HR;\n    } else {\n      const timeHours = timeDvlAidedInsMinutes / 60.0;\n      const speedMps = auvSpeedKnots * KNOTS_TO_MPS;\n      const distanceTravelledM = speedMps * timeHours * 3600;\n      return distanceTravelledM * HG9900_DVL_AIDED_DRIFT_PERCENT_DT;\n    }\n  }, [insMode, timeUnaidedInsMinutes, timeDvlAidedInsMinutes, auvSpeedKnots]);\n\n  // Calculate acoustic sigma\n  const calculateSigmaAcoustic = useCallback(() => {\n    return hipapSlantRangeM * HIPAP_ACCURACY_PERCENT_SLANT_RANGE;\n  }, [hipapSlantRangeM]);\n\n  // Get surface GNSS sigma\n  const getSigmaSurface = useCallback(() => {\n    return parseFloat(surfaceGpsQuality);\n  }, [surfaceGpsQuality]);\n\n  // Main calculation function\n  const calculateAll = useCallback(() => {\n    const newSigmaValues = {\n      ins_derived: calculateSigmaIns(),\n      acoustic_derived: calculateSigmaAcoustic(),\n      surface_derived: getSigmaSurface(),\n      gnss_offset_direct: sigmaGnssOffset,\n      ss_direct: sigmaSs,\n      deploy_direct: sigmaDeploy,\n      other_direct: sigmaOther\n    };\n    let sumOfSquares = 0;\n    const newChartData = [];\n    ERROR_SOURCES_CONFIG.forEach(config => {\n      const value = newSigmaValues[config.id];\n      let variance = 0;\n      if (!isNaN(value) && value >= 0) {\n        variance = value * value;\n        sumOfSquares += variance;\n      }\n      newChartData.push({\n        label: config.label,\n        variance: variance,\n        value: isNaN(value) || value < 0 ? 0 : value,\n        color: config.color\n      });\n    });\n    const newSigmaHTotal = Math.sqrt(sumOfSquares);\n    const newThu95 = 2 * newSigmaHTotal;\n    setSigmaValues(newSigmaValues);\n    setSigmaHTotal(newSigmaHTotal);\n    setThu95(newThu95);\n    setChartData(newChartData);\n  }, [calculateSigmaIns, calculateSigmaAcoustic, getSigmaSurface, sigmaGnssOffset, sigmaSs, sigmaDeploy, sigmaOther]);\n\n  // Recalculate when any input changes\n  useEffect(() => {\n    calculateAll();\n  }, [calculateAll]);\n  return {\n    // Input state\n    insMode,\n    setInsMode,\n    timeUnaidedInsMinutes,\n    setTimeUnaidedInsMinutes,\n    timeDvlAidedInsMinutes,\n    setTimeDvlAidedInsMinutes,\n    auvSpeedKnots,\n    setAuvSpeedKnots,\n    hipapSlantRangeM,\n    setHipapSlantRangeM,\n    surfaceGpsQuality,\n    setSurfaceGpsQuality,\n    sigmaGnssOffset,\n    setSigmaGnssOffset,\n    sigmaSs,\n    setSigmaSs,\n    sigmaDeploy,\n    setSigmaDeploy,\n    sigmaOther,\n    setSigmaOther,\n    // Calculated results\n    sigmaValues,\n    sigmaHTotal,\n    thu95,\n    chartData,\n    // Helper functions\n    calculateSigmaAcoustic,\n    getSigmaSurface\n  };\n};\n_s(useCalculations, \"Cn2ROwVLBStG16rVjsrbhcTtwIY=\");", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "HG9900_UNAIDED_DRIFT_RATE_M_PER_SIGMA_HR", "HG9900_DVL_AIDED_DRIFT_PERCENT_DT", "HIPAP_ACCURACY_PERCENT_SLANT_RANGE", "KNOTS_TO_MPS", "ERROR_SOURCES_CONFIG", "useCalculations", "_s", "insMode", "setInsMode", "timeUnaidedInsMinutes", "setTimeUnaidedInsMinutes", "timeDvlAidedInsMinutes", "setTimeDvlAidedInsMinutes", "auvSpeedKnots", "setAuvSpeedKnots", "hipapSlantRangeM", "setHipapSlantRangeM", "surfaceGpsQuality", "setSurfaceGpsQuality", "sigmaGnssOffset", "setSigmaGnssOffset", "sigmaSs", "setSigmaSs", "sigmaDeploy", "setSigmaDeploy", "sigmaOther", "setSigmaOther", "sigmaValues", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sigmaHTotal", "setSigmaHTotal", "thu95", "setThu95", "chartData", "setChartData", "calculateSigmaIns", "timeHours", "speedMps", "distanceTravelledM", "calculateSigmaAcoustic", "getSigmaSurface", "parseFloat", "calculateAll", "newSigmaV<PERSON>ues", "ins_derived", "acoustic_derived", "surface_derived", "gnss_offset_direct", "ss_direct", "deploy_direct", "other_direct", "sumOfSquares", "newChartData", "for<PERSON>ach", "config", "value", "id", "variance", "isNaN", "push", "label", "color", "newSigmaHTotal", "Math", "sqrt", "newThu95"], "sources": ["C:/Dev/TPU/auv-thu-calculator/src/hooks/useCalculations.js"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport {\n  HG9900_UNAIDED_DRIFT_RATE_M_PER_SIGMA_HR,\n  HG9900_DVL_AIDED_DRIFT_PERCENT_DT,\n  HIPAP_ACCURACY_PERCENT_SLANT_RANGE,\n  KNOTS_TO_MPS,\n  ERROR_SOURCES_CONFIG\n} from '../utils/constants';\n\nexport const useCalculations = () => {\n  // State for all input parameters\n  const [insMode, setInsMode] = useState('dvl_aided');\n  const [timeUnaidedInsMinutes, setTimeUnaidedInsMinutes] = useState(30);\n  const [timeDvlAidedInsMinutes, setTimeDvlAidedInsMinutes] = useState(30);\n  const [auvSpeedKnots, setAuvSpeedKnots] = useState(3.0);\n  const [hipapSlantRangeM, setHipapSlantRangeM] = useState(150);\n  const [surfaceGpsQuality, setSurfaceGpsQuality] = useState('2.5');\n  const [sigmaGnssOffset, setSigmaGnssOffset] = useState(0.02);\n  const [sigmaSs, setSigmaSs] = useState(0.5);\n  const [sigmaDeploy, setSigmaDeploy] = useState(1.0);\n  const [sigmaOther, setSigmaOther] = useState(0.25);\n\n  // State for calculated results\n  const [sigmaValues, setSigmaValues] = useState({});\n  const [sigmaHTotal, setSigmaHTotal] = useState(0);\n  const [thu95, setThu95] = useState(0);\n  const [chartData, setChartData] = useState([]);\n\n  // Calculate INS sigma based on mode\n  const calculateSigmaIns = useCallback(() => {\n    if (insMode === 'unaided') {\n      const timeHours = timeUnaidedInsMinutes / 60.0;\n      return timeHours * HG9900_UNAIDED_DRIFT_RATE_M_PER_SIGMA_HR;\n    } else {\n      const timeHours = timeDvlAidedInsMinutes / 60.0;\n      const speedMps = auvSpeedKnots * KNOTS_TO_MPS;\n      const distanceTravelledM = speedMps * timeHours * 3600;\n      return distanceTravelledM * HG9900_DVL_AIDED_DRIFT_PERCENT_DT;\n    }\n  }, [insMode, timeUnaidedInsMinutes, timeDvlAidedInsMinutes, auvSpeedKnots]);\n\n  // Calculate acoustic sigma\n  const calculateSigmaAcoustic = useCallback(() => {\n    return hipapSlantRangeM * HIPAP_ACCURACY_PERCENT_SLANT_RANGE;\n  }, [hipapSlantRangeM]);\n\n  // Get surface GNSS sigma\n  const getSigmaSurface = useCallback(() => {\n    return parseFloat(surfaceGpsQuality);\n  }, [surfaceGpsQuality]);\n\n  // Main calculation function\n  const calculateAll = useCallback(() => {\n    const newSigmaValues = {\n      ins_derived: calculateSigmaIns(),\n      acoustic_derived: calculateSigmaAcoustic(),\n      surface_derived: getSigmaSurface(),\n      gnss_offset_direct: sigmaGnssOffset,\n      ss_direct: sigmaSs,\n      deploy_direct: sigmaDeploy,\n      other_direct: sigmaOther\n    };\n\n    let sumOfSquares = 0;\n    const newChartData = [];\n\n    ERROR_SOURCES_CONFIG.forEach(config => {\n      const value = newSigmaValues[config.id];\n      let variance = 0;\n      if (!isNaN(value) && value >= 0) {\n        variance = value * value;\n        sumOfSquares += variance;\n      }\n      newChartData.push({\n        label: config.label,\n        variance: variance,\n        value: isNaN(value) || value < 0 ? 0 : value,\n        color: config.color\n      });\n    });\n\n    const newSigmaHTotal = Math.sqrt(sumOfSquares);\n    const newThu95 = 2 * newSigmaHTotal;\n\n    setSigmaValues(newSigmaValues);\n    setSigmaHTotal(newSigmaHTotal);\n    setThu95(newThu95);\n    setChartData(newChartData);\n  }, [\n    calculateSigmaIns,\n    calculateSigmaAcoustic,\n    getSigmaSurface,\n    sigmaGnssOffset,\n    sigmaSs,\n    sigmaDeploy,\n    sigmaOther\n  ]);\n\n  // Recalculate when any input changes\n  useEffect(() => {\n    calculateAll();\n  }, [calculateAll]);\n\n  return {\n    // Input state\n    insMode,\n    setInsMode,\n    timeUnaidedInsMinutes,\n    setTimeUnaidedInsMinutes,\n    timeDvlAidedInsMinutes,\n    setTimeDvlAidedInsMinutes,\n    auvSpeedKnots,\n    setAuvSpeedKnots,\n    hipapSlantRangeM,\n    setHipapSlantRangeM,\n    surfaceGpsQuality,\n    setSurfaceGpsQuality,\n    sigmaGnssOffset,\n    setSigmaGnssOffset,\n    sigmaSs,\n    setSigmaSs,\n    sigmaDeploy,\n    setSigmaDeploy,\n    sigmaOther,\n    setSigmaOther,\n\n    // Calculated results\n    sigmaValues,\n    sigmaHTotal,\n    thu95,\n    chartData,\n\n    // Helper functions\n    calculateSigmaAcoustic,\n    getSigmaSurface\n  };\n};\n\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACxD,SACEC,wCAAwC,EACxCC,iCAAiC,EACjCC,kCAAkC,EAClCC,YAAY,EACZC,oBAAoB,QACf,oBAAoB;AAE3B,OAAO,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,WAAW,CAAC;EACnD,MAAM,CAACY,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACtE,MAAM,CAACc,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACxE,MAAM,CAACgB,aAAa,EAAEC,gBAAgB,CAAC,GAAGjB,QAAQ,CAAC,GAAG,CAAC;EACvD,MAAM,CAACkB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnB,QAAQ,CAAC,GAAG,CAAC;EAC7D,MAAM,CAACoB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACsB,eAAe,EAAEC,kBAAkB,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,GAAG,CAAC;EAC3C,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,GAAG,CAAC;EACnD,MAAM,CAAC4B,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM,CAACgC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACkC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAACoC,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAMsC,iBAAiB,GAAGpC,WAAW,CAAC,MAAM;IAC1C,IAAIQ,OAAO,KAAK,SAAS,EAAE;MACzB,MAAM6B,SAAS,GAAG3B,qBAAqB,GAAG,IAAI;MAC9C,OAAO2B,SAAS,GAAGpC,wCAAwC;IAC7D,CAAC,MAAM;MACL,MAAMoC,SAAS,GAAGzB,sBAAsB,GAAG,IAAI;MAC/C,MAAM0B,QAAQ,GAAGxB,aAAa,GAAGV,YAAY;MAC7C,MAAMmC,kBAAkB,GAAGD,QAAQ,GAAGD,SAAS,GAAG,IAAI;MACtD,OAAOE,kBAAkB,GAAGrC,iCAAiC;IAC/D;EACF,CAAC,EAAE,CAACM,OAAO,EAAEE,qBAAqB,EAAEE,sBAAsB,EAAEE,aAAa,CAAC,CAAC;;EAE3E;EACA,MAAM0B,sBAAsB,GAAGxC,WAAW,CAAC,MAAM;IAC/C,OAAOgB,gBAAgB,GAAGb,kCAAkC;EAC9D,CAAC,EAAE,CAACa,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMyB,eAAe,GAAGzC,WAAW,CAAC,MAAM;IACxC,OAAO0C,UAAU,CAACxB,iBAAiB,CAAC;EACtC,CAAC,EAAE,CAACA,iBAAiB,CAAC,CAAC;;EAEvB;EACA,MAAMyB,YAAY,GAAG3C,WAAW,CAAC,MAAM;IACrC,MAAM4C,cAAc,GAAG;MACrBC,WAAW,EAAET,iBAAiB,CAAC,CAAC;MAChCU,gBAAgB,EAAEN,sBAAsB,CAAC,CAAC;MAC1CO,eAAe,EAAEN,eAAe,CAAC,CAAC;MAClCO,kBAAkB,EAAE5B,eAAe;MACnC6B,SAAS,EAAE3B,OAAO;MAClB4B,aAAa,EAAE1B,WAAW;MAC1B2B,YAAY,EAAEzB;IAChB,CAAC;IAED,IAAI0B,YAAY,GAAG,CAAC;IACpB,MAAMC,YAAY,GAAG,EAAE;IAEvBhD,oBAAoB,CAACiD,OAAO,CAACC,MAAM,IAAI;MACrC,MAAMC,KAAK,GAAGZ,cAAc,CAACW,MAAM,CAACE,EAAE,CAAC;MACvC,IAAIC,QAAQ,GAAG,CAAC;MAChB,IAAI,CAACC,KAAK,CAACH,KAAK,CAAC,IAAIA,KAAK,IAAI,CAAC,EAAE;QAC/BE,QAAQ,GAAGF,KAAK,GAAGA,KAAK;QACxBJ,YAAY,IAAIM,QAAQ;MAC1B;MACAL,YAAY,CAACO,IAAI,CAAC;QAChBC,KAAK,EAAEN,MAAM,CAACM,KAAK;QACnBH,QAAQ,EAAEA,QAAQ;QAClBF,KAAK,EAAEG,KAAK,CAACH,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,GAAG,CAAC,GAAGA,KAAK;QAC5CM,KAAK,EAAEP,MAAM,CAACO;MAChB,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,MAAMC,cAAc,GAAGC,IAAI,CAACC,IAAI,CAACb,YAAY,CAAC;IAC9C,MAAMc,QAAQ,GAAG,CAAC,GAAGH,cAAc;IAEnClC,cAAc,CAACe,cAAc,CAAC;IAC9Bb,cAAc,CAACgC,cAAc,CAAC;IAC9B9B,QAAQ,CAACiC,QAAQ,CAAC;IAClB/B,YAAY,CAACkB,YAAY,CAAC;EAC5B,CAAC,EAAE,CACDjB,iBAAiB,EACjBI,sBAAsB,EACtBC,eAAe,EACfrB,eAAe,EACfE,OAAO,EACPE,WAAW,EACXE,UAAU,CACX,CAAC;;EAEF;EACA3B,SAAS,CAAC,MAAM;IACd4C,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;EAElB,OAAO;IACL;IACAnC,OAAO;IACPC,UAAU;IACVC,qBAAqB;IACrBC,wBAAwB;IACxBC,sBAAsB;IACtBC,yBAAyB;IACzBC,aAAa;IACbC,gBAAgB;IAChBC,gBAAgB;IAChBC,mBAAmB;IACnBC,iBAAiB;IACjBC,oBAAoB;IACpBC,eAAe;IACfC,kBAAkB;IAClBC,OAAO;IACPC,UAAU;IACVC,WAAW;IACXC,cAAc;IACdC,UAAU;IACVC,aAAa;IAEb;IACAC,WAAW;IACXE,WAAW;IACXE,KAAK;IACLE,SAAS;IAET;IACAM,sBAAsB;IACtBC;EACF,CAAC;AACH,CAAC;AAAClC,EAAA,CA/HWD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}