<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AUV Mission Planning Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; background-color: #2c3e50; color: #ecf0f1; display: flex; flex-direction: column; height: 100vh; }
        .header { background-color: #34495e; padding: 10px 20px; text-align: center; border-bottom: 2px solid #7f8c8d; }
        .header h1 { margin: 0; font-size: 1.5em; }
        .mission-planning-container { display: flex; flex: 1; overflow: hidden; }
        .left-panel-auv { width: 25%; background-color: #34495e; padding: 15px; border-right: 2px solid #7f8c8d; overflow-y: auto; }
        .center-map-panel-auv { width: 55%; background-color: #2c3e50; padding: 15px; display: flex; flex-direction: column; }
        .right-panel-auv { width: 20%; background-color: #34495e; padding: 15px; border-left: 2px solid #7f8c8d; overflow-y: auto; }
        .panel-title { font-weight: bold; margin-bottom: 10px; padding-bottom: 5px; border-bottom: 1px solid #7f8c8d; }
        .map-placeholder-auv { flex: 1; background-color: #1a2531; border: 1px dashed #7f8c8d; display: flex; align-items: center; justify-content: center; text-align: center; font-size: 1.1em; color: #7f8c8d; }
        .form-group { margin-bottom: 12px; }
        .form-group label { display: block; margin-bottom: 4px; font-size: 0.9em; }
        .form-group input, .form-group select, .form-group textarea {
            width: calc(100% - 12px); padding: 6px; background-color: #4a627a; border: 1px solid #7f8c8d; color: #ecf0f1; border-radius: 3px; font-size:0.9em;
        }
        .form-group textarea { min-height: 60px; }
        .button-primary { background-color: #5dade2; border: none; color: white; padding: 8px 12px; margin-top:5px; border-radius: 3px; cursor: pointer; width: 100%; }
        .button-secondary { background-color: #27ae60; border: none; color: white; padding: 8px 12px; margin-top:5px; border-radius: 3px; cursor: pointer; width: 100%;}
        .mission-tabs { display: flex; margin-bottom: 10px; }
        .mission-tab-button { flex: 1; padding: 8px; background-color: #4a627a; border: 1px solid #7f8c8d; color: #ecf0f1; cursor: pointer; text-align: center;}
        .mission-tab-button.active { background-color: #5dade2; }
        .tab-content-auv { border: 1px solid #4a627a; padding:10px; margin-top:-1px /* overlap border */; }
        .endurance-panel { background-color: #34495e; padding: 10px; margin-top: 10px; border-top: 2px solid #7f8c8d; }
        .line-list-placeholder { height: 100px; background-color: #4a627a; border: 1px dashed #7f8c8d; margin-bottom:10px; padding:5px; font-size:0.8em; overflow-y: auto;}
        .battery-graph-placeholder { height: 50px; background-color: #4a627a; border: 1px dashed #7f8c8d; display:flex; align-items:center; justify-content:center; }
    </style>
</head>
<body>
    <div class="header">
        <h1>AUV Mission Planning Tool (Hugin 3000)</h1>
    </div>
    <div class="mission-planning-container">
        <div class="left-panel-auv">
            <div class="panel-title">Mission Setup & Parameters</div>
            <div class="form-group">
                <label for="missionName">Mission Name:</label>
                <input type="text" id="missionName" value="WreckSearch_Area51">
            </div>
            <div class="form-group">
                <label for="auvType">AUV Type:</label>
                <select id="auvType"><option>Kongsberg Hugin 3000</option></select>
            </div>
            <div class="form-group">
                <label for="payload">Payload Configuration:</label>
                <select id="payload"><option>MBES+SSS+SBP (Standard)</option><option>High-Res Camera</option></select>
            </div>
            <div class="form-group">
                <label for="aoi">Area of Interest (AOI):</label>
                <textarea id="aoi" placeholder="Draw on map or enter coordinates..."></textarea>
            </div>
            
            <div class="mission-tabs">
                <button class="mission-tab-button active" onclick="openAuvMode('searchMode')">Search Mode</button>
                <button class="mission-tab-button" onclick="openAuvMode('surveyMode')">Survey/Inspect Mode</button>
            </div>

            <div id="searchMode" class="tab-content-auv auv-mode-pane active">
                <div class="form-group">
                    <label>Altitude (AGL):</label> <input type="number" value="30"> meters
                </div>
                <div class="form-group">
                    <label>MBES Swath Angle:</label> <input type="number" value="140"> degrees
                </div>
                 <div class="form-group">
                    <label>SSS Range:</label> <input type="number" value="150"> meters
                </div>
                <div class="form-group">
                    <label>Overlap %:</label> <input type="number" value="10"> %
                </div>
            </div>
            <div id="surveyMode" class="tab-content-auv auv-mode-pane" style="display:none;">
                <div class="form-group">
                    <label>Altitude (AGL):</label> <input type="number" value="15"> meters
                </div>
                <div class="form-group">
                    <label>IHO Order Target:</label> <select><option>Order 1a</option><option>Special Order</option></select>
                </div>
                 <div class="form-group">
                    <label>Pipeline Route File:</label> <input type="file">
                </div>
                <div class="form-group">
                    <label>Overlap %:</label> <input type="number" value="25"> %
                </div>
            </div>
            <button class="button-primary" style="margin-top: 15px;">Generate/Update Lines</button>
        </div>

        <div class="center-map-panel-auv">
            <div class="map-toolbar" style="background-color: #2c3e50; padding:0; margin-bottom:5px;">
                <button style="background-color: #4a627a;">Zoom</button>
                <button style="background-color: #4a627a;">Pan</button>
                <button style="background-color: #4a627a;">Draw AOI</button>
                <button style="background-color: #4a627a;">Load DTM</button>
            </div>
            <div class="map-placeholder-auv">
                AUV Mission Planning Map Area<br>
                <small>(Displaying DTM, Planned Lines, Waypoints, USBL Loiter Boxes, Infrastructure)</small>
            </div>
            <div class="endurance-panel">
                <div class="panel-title" style="margin-bottom:5px;">Endurance & Navigation Plan</div>
                <div class="line-list-placeholder">
                    Line 1: StartX, StartY -> EndX, EndY | Alt: 30m | Speed: 3kts<br>
                    Line 2: StartX, StartY -> EndX, EndY | Alt: 30m | Speed: 3kts<br>
                    USBL Loiter 1: CenterX, CenterY | Duration: 10min<br>
                    ... (Scrollable list of mission legs) ...
                </div>
                <div class="form-group" style="font-size:0.9em;">
                    Est. Duration: <input type="text" value="6h 35m" readonly style="width:80px; display:inline;">
                    Battery Used: <input type="text" value="72%" readonly style="width:60px; display:inline;">
                </div>
                <div class="battery-graph-placeholder">Battery % Along Mission Track (Visual Placeholder)</div>
                <div style="display:flex; gap:10px; margin-top:10px;">
                    <button class="button-primary" style="flex:1;">Validate Mission</button>
                    <button class="button-secondary" style="flex:1;">Export Kongsberg File</button>
                    <button class="button-secondary" style="flex:1;">Generate PDF Report</button>
                </div>
            </div>
        </div>

        <div class="right-panel-auv">
            <div class="panel-title">Error Budget / TPU (Survey Mode)</div>
            <div class="form-group">
                <label>INS Drift (m/hr):</label> <input type="number" step="0.01" value="0.25">
            </div>
            <div class="form-group">
                <label>USBL Fix Acc (m):</label> <input type="number" step="0.1" value="1.5">
            </div>
            <div class="form-group">
                <label>MBES Range Acc (% slant):</label> <input type="number" step="0.01" value="0.1">
            </div>
            <div class="form-group">
                <label>Sound Velocity Uncert. (m/s):</label> <input type="number" step="0.1" value="0.5">
            </div>
            <div class="widget" style="background-color: #2c3e50; border:1px solid #4a627a; margin-top:15px; padding:10px; text-align:center;">
                <h4 style="margin-top:0;">Predicted TPU @ Nadir</h4>
                <p style="font-size:1.5em; color: #2ecc71;">0.35 m</p>
                <p><small>(Target Depth: 50m, IHO Order 1a)</small></p>
                <p style="color: #2ecc71;">Status: WITHIN SPEC</p>
            </div>
             <div class="panel-title" style="margin-top:20px;">NavAids Suggestion</div>
             <p style="font-size:0.85em;">Suggest USBL update every 45 minutes or 2000m traveled.</p>
             <p style="font-size:0.85em;">Recommended loiter box near Waypoint 15.</p>
        </div>
    </div>
    <script>
        function openAuvMode(modeName) {
            var i, tabcontent, tablinks;
            tabcontent = document.getElementsByClassName("auv-mode-pane");
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].style.display = "none";
                tabcontent[i].classList.remove("active");
            }
            tablinks = document.getElementsByClassName("mission-tab-button");
            for (i = 0; i < tablinks.length; i++) {
                tablinks[i].classList.remove("active");
            }
            document.getElementById(modeName).style.display = "block";
            document.getElementById(modeName).classList.add("active");
             for (i = 0; i < tablinks.length; i++) {
                if(tablinks[i].getAttribute('onclick') === "openAuvMode('"+modeName+"')") {
                    tablinks[i].classList.add("active");
                }
            }
        }
         document.addEventListener('DOMContentLoaded', function() {
            openAuvMode('searchMode');
        });
    </script>
</body>
</html>