import { useState, useEffect, useCallback } from 'react';
import {
  HG9900_UNAIDED_DRIFT_RATE_M_PER_SIGMA_HR,
  HG9900_DVL_AIDED_DRIFT_PERCENT_DT,
  HIPAP_ACCURACY_PERCENT_SLANT_RANGE,
  KNOTS_TO_MPS,
  ERROR_SOURCES_CONFIG
} from '../utils/constants';

export const useCalculations = () => {
  // State for all input parameters
  const [insMode, setInsMode] = useState('dvl_aided');
  const [timeUnaidedInsMinutes, setTimeUnaidedInsMinutes] = useState(30);
  const [timeDvlAidedInsMinutes, setTimeDvlAidedInsMinutes] = useState(30);
  const [auvSpeedKnots, setAuvSpeedKnots] = useState(3.0);
  const [hipapSlantRangeM, setHipapSlantRangeM] = useState(150);
  const [surfaceGpsQuality, setSurfaceGpsQuality] = useState('2.5');
  const [sigmaGnssOffset, setSigmaGnssOffset] = useState(0.02);
  const [sigmaSs, setSigmaSs] = useState(0.5);
  const [sigmaDeploy, setSigmaDeploy] = useState(1.0);
  const [sigmaOther, setSigmaOther] = useState(0.25);

  // State for calculated results
  const [sigmaValues, setSigmaValues] = useState({});
  const [sigmaHTotal, setSigmaHTotal] = useState(0);
  const [thu95, setThu95] = useState(0);
  const [chartData, setChartData] = useState([]);

  // Calculate INS sigma based on mode
  const calculateSigmaIns = useCallback(() => {
    if (insMode === 'unaided') {
      const timeHours = timeUnaidedInsMinutes / 60.0;
      return timeHours * HG9900_UNAIDED_DRIFT_RATE_M_PER_SIGMA_HR;
    } else {
      const timeHours = timeDvlAidedInsMinutes / 60.0;
      const speedMps = auvSpeedKnots * KNOTS_TO_MPS;
      const distanceTravelledM = speedMps * timeHours * 3600;
      return distanceTravelledM * HG9900_DVL_AIDED_DRIFT_PERCENT_DT;
    }
  }, [insMode, timeUnaidedInsMinutes, timeDvlAidedInsMinutes, auvSpeedKnots]);

  // Calculate acoustic sigma
  const calculateSigmaAcoustic = useCallback(() => {
    return hipapSlantRangeM * HIPAP_ACCURACY_PERCENT_SLANT_RANGE;
  }, [hipapSlantRangeM]);

  // Get surface GNSS sigma
  const getSigmaSurface = useCallback(() => {
    return parseFloat(surfaceGpsQuality);
  }, [surfaceGpsQuality]);

  // Main calculation function
  const calculateAll = useCallback(() => {
    const newSigmaValues = {
      ins_derived: calculateSigmaIns(),
      acoustic_derived: calculateSigmaAcoustic(),
      surface_derived: getSigmaSurface(),
      gnss_offset_direct: sigmaGnssOffset,
      ss_direct: sigmaSs,
      deploy_direct: sigmaDeploy,
      other_direct: sigmaOther
    };

    let sumOfSquares = 0;
    const newChartData = [];

    ERROR_SOURCES_CONFIG.forEach(config => {
      const value = newSigmaValues[config.id];
      let variance = 0;
      if (!isNaN(value) && value >= 0) {
        variance = value * value;
        sumOfSquares += variance;
      }
      newChartData.push({
        label: config.label,
        variance: variance,
        value: isNaN(value) || value < 0 ? 0 : value,
        color: config.color
      });
    });

    const newSigmaHTotal = Math.sqrt(sumOfSquares);
    const newThu95 = 2 * newSigmaHTotal;

    setSigmaValues(newSigmaValues);
    setSigmaHTotal(newSigmaHTotal);
    setThu95(newThu95);
    setChartData(newChartData);
  }, [
    calculateSigmaIns,
    calculateSigmaAcoustic,
    getSigmaSurface,
    sigmaGnssOffset,
    sigmaSs,
    sigmaDeploy,
    sigmaOther
  ]);

  // Recalculate when any input changes
  useEffect(() => {
    calculateAll();
  }, [calculateAll]);

  return {
    // Input state
    insMode,
    setInsMode,
    timeUnaidedInsMinutes,
    setTimeUnaidedInsMinutes,
    timeDvlAidedInsMinutes,
    setTimeDvlAidedInsMinutes,
    auvSpeedKnots,
    setAuvSpeedKnots,
    hipapSlantRangeM,
    setHipapSlantRangeM,
    surfaceGpsQuality,
    setSurfaceGpsQuality,
    sigmaGnssOffset,
    setSigmaGnssOffset,
    sigmaSs,
    setSigmaSs,
    sigmaDeploy,
    setSigmaDeploy,
    sigmaOther,
    setSigmaOther,

    // Calculated results
    sigmaValues,
    sigmaHTotal,
    thu95,
    chartData,

    // Helper functions
    calculateSigmaAcoustic,
    getSigmaSurface
  };
};

