import React, { useState } from 'react';
import { useCalculations } from './hooks/useCalculations';
import Header from './components/Header';
import INSConfiguration from './components/INSConfiguration';
import AcousticConfiguration from './components/AcousticConfiguration';
import SurfaceConfiguration from './components/SurfaceConfiguration';
import ErrorSources from './components/ErrorSources';
import Results from './components/Results';
import CalculationDetails from './components/CalculationDetails';
import './styles/App.css';

function App() {
  const [activeTab, setActiveTab] = useState('calculator');
  
  const {
    // Input state
    insMode,
    setInsMode,
    timeUnaidedInsMinutes,
    setTimeUnaidedInsMinutes,
    timeDvlAidedInsMinutes,
    setTimeDvlAidedInsMinutes,
    auvSpeedKnots,
    setAuvSpeedKnots,
    hipapSlantRangeM,
    setHipapSlantRangeM,
    surfaceGpsQuality,
    setSurfaceGpsQuality,
    sigmaGnssOffset,
    setSigmaGnssOffset,
    sigmaSs,
    setSigmaSs,
    sigmaDeploy,
    setSigmaDeploy,
    sigmaOther,
    setSigmaOther,

    // Calculated results
    sigmaHTotal,
    thu95,
    chartData,

    // Helper functions
    calculateSigmaAcoustic,
    getSigmaSurface
  } = useCalculations();

  return (
    <div className="app-container">
      <div className="calculator-container">
        <Header />
        
        <div className="tab-navigation">
          <button 
            className={`tab-button ${activeTab === 'calculator' ? 'active' : ''}`}
            onClick={() => setActiveTab('calculator')}
          >
            Calculator
          </button>
          <button 
            className={`tab-button ${activeTab === 'details' ? 'active' : ''}`}
            onClick={() => setActiveTab('details')}
          >
            Calculation Details
          </button>
        </div>
        
        {activeTab === 'calculator' ? (
          <main>
            {/* Existing calculator UI */}
            <div className="main-grid">
              {/* Left and Right columns remain unchanged */}
            </div>
          </main>
        ) : (
          <CalculationDetails 
            insMode={insMode}
            timeUnaidedInsMinutes={timeUnaidedInsMinutes}
            timeDvlAidedInsMinutes={timeDvlAidedInsMinutes}
            auvSpeedKnots={auvSpeedKnots}
            hipapSlantRangeM={hipapSlantRangeM}
            surfaceGpsQuality={surfaceGpsQuality}
            sigmaGnssOffset={sigmaGnssOffset}
            sigmaSs={sigmaSs}
            sigmaDeploy={sigmaDeploy}
            sigmaOther={sigmaOther}
            sigmaValues={sigmaValues}
            sigmaHTotal={sigmaHTotal}
            thu95={thu95}
          />
        )}

        <footer className="footer">
          <p>
            This calculator provides estimations. Verify all assumptions and default sensor performance values.
            Consult specific equipment documentation and IHO S-44 Ed. 6.0.0 for accurate hydrographic survey analysis.
          </p>
        </footer>
      </div>
    </div>
  );
}

export default App;

