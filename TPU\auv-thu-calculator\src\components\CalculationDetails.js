import React from 'react';
import { 
  HG9900_UNAIDED_DRIFT_RATE_M_PER_SIGMA_HR,
  HG9900_DVL_AIDED_DRIFT_PERCENT_DT,
  HIPAP_ACCURACY_PERCENT_SLANT_RANGE,
  KNOTS_TO_MPS,
  ERROR_SOURCES_CONFIG
} from '../utils/constants';

const CalculationDetails = ({
  insMode,
  timeUnaidedInsMinutes,
  timeDvlAidedInsMinutes,
  auvSpeedKnots,
  hipapSlantRangeM,
  surfaceGpsQuality,
  sigmaGnssOffset,
  sigmaSs,
  sigmaDeploy,
  sigmaOther,
  sigmaValues,
  sigmaHTotal,
  thu95
}) => {
  // Calculate values for display
  const timeUnaidedHours = timeUnaidedInsMinutes / 60.0;
  const timeDvlAidedHours = timeDvlAidedInsMinutes / 60.0;
  const speedMps = auvSpeedKnots * KNOTS_TO_MPS;
  const distanceTravelledM = speedMps * timeDvlAidedHours * 3600;
  
  return (
    <div className="calculation-details">
      <h2>Detailed Calculation Breakdown</h2>
      
      <section className="detail-section">
        <h3>1. INS Error Calculation</h3>
        {insMode === 'unaided' ? (
          <div>
            <p className="formula">σ<sub>INS</sub> = Time × Drift Rate</p>
            <p className="calculation">
              σ<sub>INS</sub> = {timeUnaidedHours.toFixed(2)} hours × {HG9900_UNAIDED_DRIFT_RATE_M_PER_SIGMA_HR} m/hr
              = {sigmaValues.ins_derived.toFixed(2)} m
            </p>
            <p className="explanation">
              For unaided INS, the error grows linearly with time as the position solution drifts.
              The HG9900 INS has a specified drift rate of {HG9900_UNAIDED_DRIFT_RATE_M_PER_SIGMA_HR} m/hr (1σ).
            </p>
          </div>
        ) : (
          <div>
            <p className="formula">σ<sub>INS</sub> = Distance Travelled × Drift Percentage</p>
            <p className="calculation">
              Speed = {auvSpeedKnots} knots = {speedMps.toFixed(2)} m/s<br />
              Distance = {speedMps.toFixed(2)} m/s × {timeDvlAidedHours.toFixed(2)} hours × 3600 s/hr = {distanceTravelledM.toFixed(2)} m<br />
              σ<sub>INS</sub> = {distanceTravelledM.toFixed(2)} m × {HG9900_DVL_AIDED_DRIFT_PERCENT_DT} = {sigmaValues.ins_derived.toFixed(2)} m
            </p>
            <p className="explanation">
              For DVL-aided INS, the error is proportional to distance traveled.
              The HG9900 with DVL aiding has a specified error of {HG9900_DVL_AIDED_DRIFT_PERCENT_DT * 100}% of distance traveled (1σ).
            </p>
          </div>
        )}
      </section>
      
      <section className="detail-section">
        <h3>2. Acoustic Positioning Error</h3>
        <p className="formula">σ<sub>Acoustic</sub> = Slant Range × Accuracy Percentage</p>
        <p className="calculation">
          σ<sub>Acoustic</sub> = {hipapSlantRangeM} m × {HIPAP_ACCURACY_PERCENT_SLANT_RANGE} = {sigmaValues.acoustic_derived.toFixed(2)} m
        </p>
        <p className="explanation">
          The HiPAP 501 acoustic positioning system has a specified accuracy of {HIPAP_ACCURACY_PERCENT_SLANT_RANGE * 100}% of slant range (1σ).
          As slant range increases, positioning accuracy decreases proportionally.
        </p>
      </section>
      
      {/* Additional sections for other error sources */}
      
      <section className="detail-section">
        <h3>Total Horizontal Uncertainty Calculation</h3>
        <p className="formula">σ<sub>H_total</sub> = √(σ<sub>1</sub>² + σ<sub>2</sub>² + ... + σ<sub>n</sub>²)</p>
        <p className="calculation">
          σ<sub>H_total</sub> = √(
          {ERROR_SOURCES_CONFIG.map(config => {
            const value = sigmaValues[config.id];
            return value && !isNaN(value) ? `${value.toFixed(2)}² + ` : '';
          }).join('').slice(0, -3)})
          = {sigmaHTotal.toFixed(2)} m
        </p>
        <p className="formula">THU<sub>95%</sub> = 2 × σ<sub>H_total</sub> = 2 × {sigmaHTotal.toFixed(2)} = {thu95.toFixed(2)} m</p>
        <p className="explanation">
          The total horizontal uncertainty is calculated as the root sum square of all contributing error sources,
          following the propagation of uncertainty principles outlined in hydrographic standards.
          The 95% confidence level (THU<sub>95%</sub>) is calculated as 2 times the 1σ value.
        </p>
      </section>
      
      <section className="detail-section">
        <h3>References</h3>
        <ul>
          <li>Lockhart, D., Saade, E., & Wilson, J. (2008). "Navigation Post Processing and TPU Calculations"</li>
          <li>NOAA Hydrographic Survey Specifications and Deliverables (2024)</li>
          <li>IHO S-44 Ed. 6.0.0 Standards for Hydrographic Surveys</li>
        </ul>
      </section>
    </div>
  );
};

export default CalculationDetails;