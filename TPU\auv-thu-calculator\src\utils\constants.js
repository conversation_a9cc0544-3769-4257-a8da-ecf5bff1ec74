// Configuration Constants (User should verify/adjust these for their specific setup)
export const HG9900_UNAIDED_DRIFT_RATE_M_PER_SIGMA_HR = 75.0; // Illustrative: 1-sigma radial error growth (m/hr)
export const HG9900_DVL_AIDED_DRIFT_PERCENT_DT = 0.001; // Illustrative: 0.1% of Distance Travelled (1-sigma)
export const HIPAP_ACCURACY_PERCENT_SLANT_RANGE = 0.002; // Illustrative: 0.2% of Slant Range (1-sigma)
export const KNOTS_TO_MPS = 0.514444;

// Additional INS constants
export const HG9900_HEADING_ACCURACY_DEG = 0.05; // 0.05° heading accuracy (1σ)
export const HG9900_PITCH_ROLL_ACCURACY_DEG = 0.02; // 0.02° pitch/roll accuracy (1σ)

// Sound velocity error constants
export const SVP_MEASUREMENT_ERROR_M_S = 0.02; // 0.02 m/s measurement error (1σ)
export const SVP_SPATIAL_TEMPORAL_ERROR_M_S = 2.0; // 2.0 m/s spatial/temporal variation (1σ)

// Lever arm measurement constants
export const LEVER_ARM_MEASUREMENT_ERROR_M = 0.01; // 1cm measurement error (1σ)

export const IHO_S44_LIMITS = {
  // THU95% limits in meters
  exclusive: 0.5,
  special: 1.0,
  '1a': 2.0,
  '1b': 5.0,
  '2a': 10.0,
  '2b': 20.0,
  none: Infinity
};

export const ERROR_SOURCES_CONFIG = [
  { id: 'ins_derived', label: 'INS Performance (HG9900)', color: '#EF4444' }, // red-500
  { id: 'acoustic_derived', label: 'Acoustic Fix (HiPAP)', color: '#F97316' }, // orange-500
  { id: 'surface_derived', label: 'Surface GNSS Fix', color: '#84CC16' }, // lime-500
  { id: 'gnss_offset_direct', label: 'GNSS Offset Error', color: '#F59E0B' }, // amber-500
  { id: 'ss_direct', label: 'Sound Speed (Valeport)', color: '#22C55E' }, // green-500
  { id: 'deploy_direct', label: 'Deployment/Cal.', color: '#14B8A6' }, // teal-500
  { id: 'other_direct', label: 'Other Unmodeled', color: '#6366F1' } // indigo-500
];

// Expanded error sources configuration
export const EXPANDED_ERROR_SOURCES_CONFIG = [
  ...ERROR_SOURCES_CONFIG,
  {
    id: 'lever_arm_error',
    label: 'Lever Arm Error',
    color: '#8e44ad'
  },
  {
    id: 'svp_error',
    label: 'Sound Velocity Error',
    color: '#d35400'
  },
  {
    id: 'attitude_error',
    label: 'Attitude Error',
    color: '#27ae60'
  }
];

export const GNSS_QUALITY_OPTIONS = [
  { value: '2.5', label: 'Uncorrected GNSS (σ ≈ 2.5m)' },
  { value: '0.5', label: 'Differential GNSS (DGPS/RTK Float) (σ ≈ 0.5m)' },
  { value: '0.05', label: 'Veripos Apex/Ultra (PPP/RTK Fixed) (σ ≈ 0.05m)' }
];

export const IHO_ORDER_OPTIONS = [
  { value: 'exclusive', label: 'Exclusive Order (0.5m)' },
  { value: 'special', label: 'Special Order (1.0m)' },
  { value: '1a', label: 'Order 1a (2.0m)' },
  { value: '1b', label: 'Order 1b (5.0m)' },
  { value: '2a', label: 'Order 2a (10.0m)' },
  { value: '2b', label: 'Order 2b (20.0m)' },
  { value: 'none', label: 'None (No Check)' }
];

// IHO S-44 Ed. 6.0.0 constants (expanded)
export const IHO_S44_DEPTH_DEPENDENT = {
  'exclusive': { a: 0.5, b: 0.0075 }, // a + b*d
  'special': { a: 1.0, b: 0.015 },
  '1a': { a: 2.0, b: 0.023 },
  '1b': { a: 5.0, b: 0.05 },
  '2a': { a: 10.0, b: 0.05 },
  '2b': { a: 20.0, b: 0.10 },
  'none': { a: Infinity, b: 0 }
};

// Company logos (using high-quality placeholder URLs that can be easily replaced)
export const COMPANY_LOGOS = {
  oceanInfinity: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=200&h=80&fit=crop&crop=center&auto=format&q=80',
  honeywell: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=150&h=60&fit=crop&crop=center&auto=format&q=80',
  kongsberg: 'https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=150&h=60&fit=crop&crop=center&auto=format&q=80',
  valeport: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=150&h=60&fit=crop&crop=center&auto=format&q=80'
};

